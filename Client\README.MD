# BrickBix Client

This is the frontend application for the BrickBix real estate platform, built with React, TypeScript, and the Refine framework.

## Features

- Modern, responsive UI built with Material-UI
- Property listings and management
- Exclusive properties section with enhanced details
- User authentication and profile management
- Advanced search and filtering
- Analytics dashboard
- Mobile-optimized experience

## Tech Stack

- React 18
- TypeScript
- Refine Framework
- Material-UI (MUI)
- Framer Motion for animations
- React Hook Form with Zod validation
- Axios for API requests
- Vite for development and building

## Getting Started

### Prerequisites

- Node.js (v16 or later)
- npm or yarn
- BrickBix Server running (for API access)

### Environment Setup

Create a `.env` file in the root directory with:

```
VITE_API_URL=http://localhost:8080
```

### Installation

1. Install dependencies:
   ```bash
   npm install
   ```

2. Start the development server:
   ```bash
   npm run dev
   ```

3. Build for production:
   ```bash
   npm run build
   ```

## Project Structure

- `src/assets`: Static assets like images and icons
- `src/components`: Reusable UI components
- `src/contexts`: React context providers
- `src/hooks`: Custom React hooks
- `src/interfaces`: TypeScript type definitions
- `src/layouts`: Page layout components
- `src/pages`: Page components
- `src/services`: API service functions
- `src/theme`: MUI theme customization
- `src/utils`: Utility functions

## Key Components

- `PropertyCard`: Displays property information in a card format
- `ExclusiveForm`: Form for creating/editing exclusive properties
- `ShareButton`: Component for sharing properties
- `MetaTags`: Dynamic meta tags for SEO and social sharing

## Available Scripts

- `npm run dev`: Start development server
- `npm run build`: Build for production
- `npm run preview`: Preview production build
- `npm run lint`: Run ESLint
- `npm run lint:fix`: Fix ESLint issues
- `npm run format`: Format code with Prettier
- `npm run type-check`: Check TypeScript types
