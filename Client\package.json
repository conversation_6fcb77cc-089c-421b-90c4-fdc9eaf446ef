{"name": "clientSide", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@emotion/react": "^11.8.2", "@emotion/styled": "^11.8.1", "@hookform/resolvers": "^5.0.1", "@microsoft/clarity": "^1.0.0", "@mui/icons-material": "^5.8.3", "@mui/lab": "^5.0.0-alpha.85", "@mui/material": "^5.8.6", "@mui/x-data-grid": "^6.6.0", "@pankod/refine": "^2.7.8", "@refinedev/cli": "^2.16.29", "@refinedev/core": "^4.54.0", "@refinedev/devtools": "^1.1.37", "@refinedev/kbar": "^1.3.6", "@refinedev/mui": "^5.14.4", "@refinedev/react-hook-form": "^4.8.14", "@refinedev/react-router-v6": "^4.6.0", "@refinedev/simple-rest": "^5.0.1", "antd": "^5.16.2", "apexcharts": "^3.48.0", "axios": "^1.6.2", "framer-motion": "^11.18.2", "lucide-react": "^0.462.0", "query-string": "^7.1.1", "ra-data-simple-rest": "^5.6.4", "react": "^18.0.0", "react-admin": "^5.6.4", "react-apexcharts": "^1.4.1", "react-dom": "^18.0.0", "react-hook-form": "^7.30.0", "react-image-file-resizer": "^0.4.8", "react-responsive-carousel": "^3.2.23", "react-router-dom": "^6.8.1", "refine": "^0.0.1-alpha", "swizzle": "^1.1.0", "tailwindcss": "^3.4.16", "zod": "^3.24.3"}, "devDependencies": {"@types/node": "^18.16.2", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.38.0", "eslint-config-prettier": "^8.8.0", "eslint-import-resolver-typescript": "^3.5.5", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsdoc": "^46.4.3", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "prettier": "^2.8.8", "typescript": "^4.7.4", "vite": "^4.3.1"}, "scripts": {"dev": "refine dev", "build": "tsc && refine build", "preview": "refine start", "refine": "refine", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss}\"", "type-check": "tsc --noEmit"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "refine": {"projectId": "9ZwJQG-jx6vU5-n7SNKo"}}