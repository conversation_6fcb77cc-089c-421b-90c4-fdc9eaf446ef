import { useEffect } from "react";
import {
  Authenticated,
  Refine,
  AuthProvider,
  NotificationProvider,
} from "@refinedev/core";
import { DevtoolsProvider } from "@refinedev/devtools";
import { RefineKbar, RefineKbarProvider } from "@refinedev/kbar";
import { createTheme, ThemeProvider } from '@mui/material/styles';
import { AccountCircleOutlined,
        PeopleAltOutlined,
        StarBorderOutlined,
        VillaOutlined,
        ContactPage,
} from "@mui/icons-material";
import DashboardIcon from '@mui/icons-material/Dashboard';
import RoofingIcon from '@mui/icons-material/Roofing';
import {
  ErrorComponent,
  RefineSnackbarProvider,
  ThemedLayoutV2,
} from "@refinedev/mui";

import { Sider } from "./components/layout/sider";

import CssBaseline from "@mui/material/CssBaseline";
import GlobalStyles from "@mui/material/GlobalStyles";
import {
  CatchAllNavigate,
  NavigateToResource,
  UnsavedChangesNotifier,
} from "@refinedev/react-router-v6";
import dataProvider from "@refinedev/simple-rest";
import axios from "axios";
import { BrowserRouter, Outlet, Route, Routes } from "react-router-dom";
import { Header } from "./components/layout/header";
import { ColorModeContextProvider } from "./contexts/color-mode";
import { CredentialResponse } from "./interfaces/google";
import { Login } from "./pages/login";
import  Home  from './pages/home';
import { AllProperties } from "./pages/all-properties";
import Requirement from "./pages/all-requirement";
import  MyProfile  from "./pages/my-profile";
import { CreateProperty } from "./pages/create-property";
import EditProperty from "./pages/edit-property";
import  PropertyDetails  from "./pages/property-details";
import { ExclusiveProperties } from "./pages/exclusive-properties";
import routerProvider from "@refinedev/react-router-v6";
import Agents from "./pages/agents";
import { CreateRequirement } from "./pages/create-requirement";
import EditRequirement from "./pages/edit-requirement";
import RequirementDetails from "./pages/requirement-details";
import ContactUs from "./pages/contact-us";
import TermsAndConditions from "./pages/terms-and-conditions";
import { Footer } from "antd/es/layout/layout";
import { Link } from "react-router-dom"; // Ensure you import Link
import Clarity from '@microsoft/clarity';
import AgentProfile from "./pages/agent-profile";
import ExclusiveForm from "./components/common/ExclusiveForm";
import ExclusivePropertyDetails from "./pages/exclusive-property-details";


const axiosInstance = axios.create();
axiosInstance.interceptors.request.use((config) => {
  const token = localStorage.getItem("token");
  if (config.headers) {
    config.headers["Authorization"] = `Bearer ${token}`;
  }

  return config;
});

const projectId = "phz0hsvupx"
Clarity.init(projectId);


const theme = createTheme({
  palette: {
    primary: {
      light: '#5270bc',
      main: '#11418a',
      dark: '#0b2d60',
      contrastText: '#fff',
    },
    secondary: {
      light: '#FCFCFC',
      main: '#f44336',
      dark: '#ba000d',
      contrastText: '#000',
    },
  },
});

// Add WebSocket configuration
declare global {
  interface Window {
    EventEmitter?: {
      defaultMaxListeners: number;
    };
  }
}

if (typeof window !== 'undefined' && window.EventEmitter) {
  window.EventEmitter.defaultMaxListeners = 15;
}

function App() {
  const apiUrl = import.meta.env.VITE_API_URL;

  // Create a custom notification provider using RefineSnackbarProvider
  const notificationProvider: NotificationProvider = {
    open: ({ message, type, description }) => {
      // Map Refine notification types to MUI alert types
      const alertType = type === "success" ? "success" :
                        type === "error" ? "error" :
                        type === "progress" ? "info" : "warning";

      // Log to console for debugging
      console.log(`[${alertType}] ${message}`, description);

      // Return a function to close the notification
      return { close: () => {} };
    },
    close: () => {
      // This method is required but can be empty
      return;
    }
  };

    // Utility function to parse JWT token
    const parseJwt = (token: string) => {
      try {
        const base64Url = token.split(".")[1];
        if (!base64Url) {
          throw new Error("Invalid token format");
        }
        return JSON.parse(atob(base64Url));
      } catch (error) {
        console.error("Error parsing JWT", error);
        return null;
      }
    };

    // Utility function for API requests
    const apiRequest = async (url: string, options: RequestInit) => {
      try {
        const response = await fetch(url, options);
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data?.message || "API request failed");
        }

        return data;
      } catch (error) {
        console.error("API Request Error:", error);
        throw error;
      }
    };

    // Authentication provider
    const authProvider: AuthProvider = {
      login: async ({ credential }: CredentialResponse) => {
        if (!credential) {
          console.error("Login failed: No credential provided");
          return { success: false };
        }

        const profileObj = parseJwt(credential);

        if (!profileObj) {
          console.error("Login failed: Invalid JWT");
          return { success: false };
        }

        try {
          // API call to save user information
          const data = await apiRequest(`${apiUrl}/api/v1/users`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              name: profileObj.name,
              email: profileObj.email,
              avatar: profileObj.picture,
            }),
          });

          // We'll check superuser status from the server response
          const isSuperUser = data.isSuperUser;

          // Save user and token in local storage with superuser status
          localStorage.setItem("user", JSON.stringify({
            ...profileObj,
            userid: data._id,
            isSuperUser: isSuperUser,
            loginTime: new Date().toISOString()
          }));
          localStorage.setItem("token", credential);

          return { success: true, redirectTo: "/" };
        } catch (error) {
          console.error("Login error:", error);
          return { success: false };
        }
      },

      logout: async () => {
        const token = localStorage.getItem("token");

        if (token && typeof window !== "undefined") {
          try {
            localStorage.removeItem("token");
            localStorage.removeItem("user");
            axios.defaults.headers.common = {}; // Clear axios headers
            if (window.google?.accounts?.id) {
              (window.google.accounts.id as any).revoke(token, () => {
                console.log("Token revoked");
              });
            }
          } catch (error) {
            console.error("Logout error:", error);
          }
        }

        return { success: true, redirectTo: "/login" };
      },

      check: async () => {
        const token = localStorage.getItem("token");

        if (!token) {
          return {
            authenticated: false,
            error: {
              message: "Authentication failed: Token not found",
              name: "AuthError",
            },
            logout: true,
            redirectTo: "/login",
          };
        }

        try {
          // Check token expiration
          const user = localStorage.getItem("user");
          if (user) {
            const userData = JSON.parse(user);
            const loginTime = userData.loginTime ? new Date(userData.loginTime) : null;

            // If login time exists and is more than 7 days old, consider the token expired
            if (loginTime && (new Date().getTime() - loginTime.getTime() > 7 * 24 * 60 * 60 * 1000)) {
              localStorage.removeItem("token");
              localStorage.removeItem("user");

              return {
                authenticated: false,
                error: {
                  message: "Your session has expired. Please log in again.",
                  name: "SessionExpired",
                },
                logout: true,
                redirectTo: "/login",
              };
            }
          }

          return { authenticated: true };
        } catch (error) {
          console.error("Auth check error:", error);

          return {
            authenticated: false,
            error: {
              message: "Authentication check failed",
              name: "AuthError",
            },
            logout: true,
            redirectTo: "/login",
          };
        }
      },

      getIdentity: async () => {
        const user = localStorage.getItem("user");
        return user ? JSON.parse(user) : null;
      },

      getPermissions: async () => {
        const user = localStorage.getItem("user");
        if (!user) return null;

        const userData = JSON.parse(user);

        // Use the superuser status from the user data
        // This was set during login based on the server response
        const isSuperUser = userData.isSuperUser === true;

        return isSuperUser ? ["admin"] : ["user"];
      },

      onError: async (error: any) => {
        console.error("Auth error:", error);
        return { error };
      },
    };

  useEffect(() => {
    document.title = "BrickBix"; // Set the document title
  }, []);

  return (
    <BrowserRouter>
      <ThemeProvider theme={theme}>
      <RefineKbarProvider>
        <ColorModeContextProvider>
          <CssBaseline />
          <GlobalStyles styles={{ body: { backgroundColor: '#FCFCFC' } }} />
          <GlobalStyles styles={{ html: { WebkitFontSmoothing: "auto" } }} />
          <RefineSnackbarProvider>
            {import.meta.env.DEV ? (
              <DevtoolsProvider>
                <Refine
                  dataProvider={dataProvider(`${apiUrl}/api/v1`)}
                  notificationProvider={notificationProvider}
                  routerProvider={routerProvider}
                  authProvider={authProvider}
                  resources={[
                    {
                      name: "dashboard",
                      list: "/",
                      meta: {
                        label: 'Home',
                        icon: <DashboardIcon/>
                      }
                    },
                    {
                      name: "properties",
                      list: "/allProperties",
                      create: "/allProperties/properties/create",
                      edit: "/allProperties/properties/edit/:id",
                      show: "/properties/show/:id",
                      meta: {
                        icon: <VillaOutlined />,
                        canDelete: true
                      }
                    },
                    {
                      name: "requirement",
                      list: "/requirement",
                      create: "/requirement/properties-requirement/create",
                      edit: "/requirement/properties-requirement/edit/:id",
                      show: "/properties-requirement/show/:id",
                      meta: {
                        icon: <RoofingIcon/>
                      }
                    },
                    {
                      name: "agent",
                      list: "/agent",
                      show: "/agents/show/:id",
                      meta: {
                        icon: <PeopleAltOutlined />
                      }
                    },
                    {
                      name: "exclusive-properties",
                      list: "/exclusive",
                      create: "/create-exclusive-property",
                      edit: "/exclusive/edit/:id",
                      show: "/exclusive/:id",
                      meta: {
                        label: 'Exclusive Property',
                        icon: <StarBorderOutlined />,
                        // Only allow superusers to create exclusive properties
                        canCreate: ({ permissions }: { permissions?: string[] }) => permissions?.includes("admin")
                      }
                    },
                    {
                      name: "my-profile",
                      list: "/my-profile",
                      meta: {
                        label: 'My Profile',
                        icon: <AccountCircleOutlined />
                      }
                    },
                    {
                      name: "contact-us",
                      list: "/contact-us",
                      meta: {
                        label: 'Contact Us',
                        icon: <ContactPage />
                      }
                    },
                  ]}


                  options={{
                    syncWithLocation: true,
                    warnWhenUnsavedChanges: true,
                    useNewQueryKeys: true,
                    projectId: "9ZwJQG-jx6vU5-n7SNKo",
                  }}
                >
                  <Routes>
                    <Route
                      element={
                        <Authenticated
                          key="authenticated-inner"
                          fallback={<CatchAllNavigate to="/login" />}
                        >
                        <ThemeProvider theme={theme}>
                          <ThemedLayoutV2 Title={() => <div><span>BrickBix</span></div>} Header={() => <Header sticky />} Sider={()=><Sider />} >
                            <Outlet />
                          </ThemedLayoutV2>
                          </ThemeProvider>
                        </Authenticated>
                      }
                    >

                      <Route index element={<Home />} />
                      <Route path="/allProperties">
                        <Route index element={<AllProperties />} />
                        <Route path="properties/create" element={<CreateProperty />} />
                        <Route index path="properties/edit/:id" element={<EditProperty />} />
                      </Route>
                      <Route index path="properties/show/:id" element={<PropertyDetails />} />
                      <Route path="/requirement">
                        <Route index element={<Requirement />} />
                        <Route path="properties-requirement/create" element={<CreateRequirement />} />
                        <Route index path="properties-requirement/edit/:id" element={<EditRequirement />} />
                      </Route>
                      <Route index path="properties-requirement/show/:id" element={<RequirementDetails />} />
                      <Route path="/agent">
                        <Route index element={<Agents/>} />
                      </Route>
                      <Route path="/agents/show/:id" element={<AgentProfile />} />
                      <Route path="/exclusive">
                        <Route index element={<ExclusiveProperties/>} />
                        <Route path=":id" element={<ExclusivePropertyDetails />} />
                        <Route path="edit/:id" element={<ExclusiveForm />} />
                      </Route>
                      <Route path="/create-exclusive-property">
                        <Route index element={<ExclusiveForm />}/>
                      </Route>
                      <Route path="/my-profile">
                        <Route index element={<MyProfile />} />
                      </Route>
                      <Route path="/contact-us">
                        <Route index element={<ContactUs />} />
                      </Route>
                      <Route path="/terms-and-conditions">
                        <Route index element={<TermsAndConditions />} />
                      </Route>
                      <Route path="*" element={<ErrorComponent />} />
                    </Route>
                    <Route
                      element={
                        <Authenticated
                          key="authenticated-outer"
                          fallback={<Outlet />}
                        >
                          <NavigateToResource />
                        </Authenticated>
                      }
                    >
                      <Route path="/login" element={<Login />} />
                    </Route>
                  </Routes>
                  <RefineKbar />
                  <UnsavedChangesNotifier />
                </Refine>
              </DevtoolsProvider>
            ) : (
              <Refine
                dataProvider={dataProvider(`${apiUrl}/api/v1`)}
                notificationProvider={notificationProvider}
                routerProvider={routerProvider}
                authProvider={authProvider}
                resources={[
                  {
                    name: "dashboard",
                    list: "/",
                    meta: {
                      label: 'Home',
                      icon: <DashboardIcon/>
                    }
                  },
                  {
                    name: "properties",
                    list: "/allProperties",
                    create: "/allProperties/properties/create",
                    edit: "/allProperties/properties/edit/:id",
                    show: "/properties/show/:id",
                    meta: {
                      icon: <VillaOutlined />,
                      canDelete: true
                    }
                  },
                  {
                    name: "requirement",
                    list: "/requirement",
                    create: "/requirement/properties-requirement/create",
                    edit: "/requirement/properties-requirement/edit/:id",
                    show: "/properties-requirement/show/:id",
                    meta: {
                      icon: <RoofingIcon/>
                    }
                  },
                  {
                    name: "agent",
                    list: "/agent",
                    show: "/agents/show/:id",
                    meta: {
                      icon: <PeopleAltOutlined />
                    }
                  },
                  {
                    name: "exclusive-properties",
                    list: "/exclusive",
                    create: "/create-exclusive-property",
                    edit: "/exclusive/edit/:id",
                    show: "/exclusive/:id",
                    meta: {
                      label: 'Exclusive Property',
                      icon: <StarBorderOutlined />,
                      // Only allow superusers to create exclusive properties
                      canCreate: ({ permissions }: { permissions?: string[] }) => permissions?.includes("admin")
                    }
                  },
                  {
                    name: "my-profile",
                    list: "/my-profile",
                    meta: {
                      label: 'My Profile',
                      icon: <AccountCircleOutlined />
                    }
                  },
                  {
                    name: "contact-us",
                    list: "/contact-us",
                    meta: {
                      label: 'Contact Us',
                      icon: <ContactPage />
                    }
                  },
                ]}


                options={{
                  syncWithLocation: true,
                  warnWhenUnsavedChanges: true,
                  useNewQueryKeys: true,
                  projectId: "9ZwJQG-jx6vU5-n7SNKo",
                }}
              >
                <Routes>
                  <Route
                    element={
                      <Authenticated
                        key="authenticated-inner"
                        fallback={<CatchAllNavigate to="/login" />}
                      >
                      <ThemeProvider theme={theme}>
                        <ThemedLayoutV2 Title={() => <div><span>BrickBix</span></div>} Header={() => <Header sticky />} Sider={()=><Sider />} >
                          <Outlet />
                        </ThemedLayoutV2>
                        </ThemeProvider>
                      </Authenticated>
                    }
                  >

                    <Route index element={<Home />} />
                    <Route path="/allProperties">
                      <Route index element={<AllProperties />} />
                      <Route path="properties/create" element={<CreateProperty />} />
                      <Route index path="properties/edit/:id" element={<EditProperty />} />
                    </Route>
                    <Route index path="properties/show/:id" element={<PropertyDetails />} />
                    <Route path="/requirement">
                      <Route index element={<Requirement />} />
                      <Route path="properties-requirement/create" element={<CreateRequirement />} />
                      <Route index path="properties-requirement/edit/:id" element={<EditRequirement />} />
                    </Route>
                    <Route index path="properties-requirement/show/:id" element={<RequirementDetails />} />
                    <Route path="/agent">
                      <Route index element={<Agents/>} />
                    </Route>
                    <Route path="/agents/show/:id" element={<AgentProfile />} />
                    <Route path="/exclusive">
                      <Route index element={<ExclusiveProperties/>} />
                      <Route path=":id" element={<ExclusivePropertyDetails />} />
                      <Route path="edit/:id" element={<ExclusiveForm />} />
                    </Route>
                    <Route path="/create-exclusive-property">
                      <Route index element={<ExclusiveForm />}/>
                    </Route>
                    <Route path="/my-profile">
                      <Route index element={<MyProfile />} />
                    </Route>
                    <Route path="/contact-us">
                      <Route index element={<ContactUs />} />
                    </Route>
                    <Route path="/terms-and-conditions">
                      <Route index element={<TermsAndConditions />} />
                    </Route>

                    <Route path="*" element={<ErrorComponent />} />
                  </Route>
                  <Route
                    element={
                      <Authenticated
                        key="authenticated-outer"
                        fallback={<Outlet />}
                      >
                        <NavigateToResource />
                      </Authenticated>
                    }
                  >
                    <Route path="/login" element={<Login />} />
                  </Route>
                </Routes>
                <RefineKbar />
                <UnsavedChangesNotifier />
              </Refine>
            )}
          </RefineSnackbarProvider>
          <Footer style={{ textAlign: 'center', position: 'relative', bottom: '0', width: '100%' }}>
            © {new Date().getFullYear()} BrickBix Technologies. All rights reserved. |
            <Link to="/terms-and-conditions" style={{ marginLeft: '5px', color: '#1890ff', textDecoration: 'underline' }}>
              Terms and Conditions
            </Link>
          </Footer>
        </ColorModeContextProvider>
      </RefineKbarProvider>
      </ThemeProvider>
    </BrowserRouter>
  );
}

export default App;