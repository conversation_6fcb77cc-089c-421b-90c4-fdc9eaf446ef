import EmailOutlined from "@mui/icons-material/EmailOutlined";
import LocationCity from "@mui/icons-material/LocationCity";
import Phone from "@mui/icons-material/Phone";
import { useGetIdentity } from "@refinedev/core";
import Box from "@mui/material/Box";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import { useActiveAuthProvider } from "@refinedev/core";
import { AgentCardProp, InfoBarProps } from "../../interfaces/agent";
import { Link } from "react-router-dom";
import BusinessCenter from "@mui/icons-material/BusinessCenter";
import Home from "@mui/icons-material/Home";
import StorefrontIcon from "@mui/icons-material/Storefront";
import ApartmentIcon from "@mui/icons-material/Apartment";
import Chip from "@mui/material/Chip";
import { Card, CardContent, Button, Divider, Avatar, useMediaQuery, useTheme } from "@mui/material";
// Removed unused import: PersonIcon
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";

function checkImage(url: any) {
  const img = new Image();
  img.src = url;
  return img.width !== 0 && img.height !== 0;
}

const InfoBar = ({ icon, name }: InfoBarProps) => (
  <Stack flex={1} minWidth={{ xs: "100%", sm: 200 }} gap={1} direction="row" alignItems="center" sx={{ my: 0.5 }}>
    {icon}
    <Typography fontSize={14} color="#555" sx={{ wordBreak: "break-word" }}>
      {name}
    </Typography>
  </Stack>
);

const AgentCard = ({
  id,
  name,
  email,
  avatar,
  noOfProperties,
  workLocation,
  services,
}: AgentCardProp) => {
  const theme = useTheme();
  // Removed unused variable: isMobile
  // const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const authProvider = useActiveAuthProvider();
  const { data: currentUser } = useGetIdentity({
    v3LegacyAuthProviderCompatible: Boolean(authProvider?.isLegacy),
  });

  const generateLink = () => {
    if (currentUser?.email === email) return "/my-profile";
    return `/agents/show/${id}`;
  };

  return (
    <Card
      elevation={2}
      sx={{
        height: "100%",
        borderRadius: "12px",
        overflow: "hidden",
        transition: "all 0.3s ease",
        "&:hover": {
          boxShadow: "0 10px 20px rgba(0,0,0,0.1)",
        },
      }}
    >
      <Box
        sx={{
          position: "relative",
          height: "140px",
          background: "linear-gradient(45deg, #11418a 30%, #1976d2 90%)",
          display: "flex",
          justifyContent: "center",
          p: 2,
          pt: 4,
        }}
      >
        <Avatar
          src={
            checkImage(avatar)
              ? avatar
              : "https://upload.wikimedia.org/wikipedia/commons/thumb/5/59/User-avatar.svg/2048px-User-avatar.svg.png"
          }
          alt={name}
          sx={{
            width: 100,
            height: 100,
            border: "4px solid white",
            position: "absolute",
            top: "60%",
            zIndex: 2,
            boxShadow: "0 4px 10px rgba(0,0,0,0.1)",
          }}
        />
      </Box>

      <CardContent
        sx={{
          pt: 8,
          px: 3,
          pb: 3,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
        }}
      >
        <Typography
          variant="h5"
          fontWeight={600}
          color="#11142d"
          textAlign="center"
          mb={0.5}
          sx={{ wordBreak: "break-word" }}
        >
          {name}
        </Typography>

        <Typography
          variant="body2"
          color="text.secondary"
          textAlign="center"
          mb={2}
        >
          Real Estate Agent
        </Typography>

        <Divider sx={{ width: "100%", mb: 2 }} />

        <Box width="100%" mb={2}>
          <InfoBar
            icon={<EmailOutlined sx={{ color: "#1976d2", fontSize: 20 }} />}
            name={email}
          />
          <InfoBar
            icon={<LocationCity sx={{ color: "#1976d2", fontSize: 20 }} />}
            name={workLocation}
          />
          <InfoBar
            icon={<Phone sx={{ color: "#1976d2", fontSize: 20 }} />}
            name={`${noOfProperties} Properties`}
          />
        </Box>

        {services && Object.values(services).some(Boolean) && (
          <>
            <Divider sx={{ width: "100%", mb: 2 }} />
            <Typography
              fontWeight={500}
              color="#11142d"
              fontSize={14}
              mb={1}
              alignSelf="flex-start"
            >
              Services Offered:
            </Typography>
            <Stack
              direction="row"
              flexWrap="wrap"
              gap={1}
              width="100%"
              mb={2}
            >
              {services.buySell && (
                <Chip
                  icon={<StorefrontIcon />}
                  label="Buy/Sell"
                  size="small"
                  color="primary"
                  variant="outlined"
                />
              )}
              {services.rent && (
                <Chip
                  icon={<Home />}
                  label="Rent"
                  size="small"
                  color="primary"
                  variant="outlined"
                />
              )}
              {services.commercial && (
                <Chip
                  icon={<BusinessCenter />}
                  label="Commercial"
                  size="small"
                  color="primary"
                  variant="outlined"
                />
              )}
              {services.residential && (
                <Chip
                  icon={<ApartmentIcon />}
                  label="Residential"
                  size="small"
                  color="primary"
                  variant="outlined"
                />
              )}
            </Stack>
          </>
        )}

        <Button
          component={Link}
          to={generateLink()}
          variant="contained"
          color="primary"
          fullWidth
          sx={{
            mt: "auto",
            py: 1,
            borderRadius: "8px",
            textTransform: "none",
            fontWeight: 600,
          }}
          endIcon={<ArrowForwardIcon />}
        >
          {currentUser?.email === email ? "My Profile" : "View Profile"}
        </Button>
      </CardContent>
    </Card>
  );
};

export default AgentCard;
