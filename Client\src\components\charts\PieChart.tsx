import ReactApex<PERSON>hart from "react-apexcharts";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Stack from "@mui/material/Stack";
import { useTheme, useMediaQuery, Paper } from "@mui/material";

import { PieChartProps } from "../../interfaces/home";

const PieChart = ({ title, value, series, colors }: PieChartProps) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));

  // Determine chart size based on screen size
  const chartWidth = isMobile ? '100px' : isTablet ? '110px' : '130px';

  return (
    <Paper
      elevation={3}
      sx={{
        width: '100%',
        height: '100%',
        borderRadius: { xs: 2, sm: 3 },
        overflow: 'hidden',
        backgroundColor: '#ffffff',
        border: '1px solid rgba(0, 0, 0, 0.05)',
      }}
    >
      <Box
        id="chart"
        display="flex"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
        p={2}
        width="100%"
        height="100%"
        sx={{
          transition: 'all 0.3s ease',
        }}
      >
        {/* Value Display - Prominently shown at the top */}
        <Typography
          variant="h4"
          textAlign="center"
          sx={{
            color: '#0F52BA',
            fontWeight: 700,
            fontSize: { xs: '1.5rem', sm: '1.75rem', md: '2rem' },
            lineHeight: 1.2,
            mb: 1,
            textShadow: '0 1px 2px rgba(0,0,0,0.05)',
          }}
        >
          {value}
        </Typography>

        {/* Chart */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            width: '100%',
            mb: 1,
            position: 'relative',
          }}
        >
          <ReactApexChart
            options={{
              chart: {
                type: "donut",
                background: 'transparent',
                animations: {
                  enabled: true,
                  speed: 500,
                  animateGradually: {
                    enabled: true,
                    delay: 150
                  },
                  dynamicAnimation: {
                    enabled: true,
                    speed: 350
                  }
                }
              },
              colors,
              legend: { show: false },
              dataLabels: { enabled: false },
              plotOptions: {
                pie: {
                  donut: {
                    size: '75%',
                    background: 'transparent',
                    labels: {
                      show: false
                    }
                  }
                }
              },
              stroke: {
                width: 0
              },
              tooltip: {
                enabled: true,
                style: {
                  fontSize: '12px',
                  fontFamily: theme.typography.fontFamily
                }
              },
              states: {
                hover: {
                  filter: {
                    type: 'darken',
                    value: 0.9,
                  }
                },
                active: {
                  filter: {
                    type: 'darken',
                    value: 0.9,
                  }
                }
              }
            }}
            series={series}
            type="donut"
            width={chartWidth}
            height={isMobile ? '100px' : '120px'}
          />
        </Box>

        {/* Title */}
        <Typography
          variant="subtitle2"
          textAlign="center"
          sx={{
            color: '#11142d',
            fontWeight: 600,
            fontSize: { xs: '0.85rem', sm: '0.9rem', md: '1rem' },
            lineHeight: 1.3,
            mt: 0.5,
            px: 1,
            width: '100%',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        >
          {title}
        </Typography>
      </Box>
    </Paper>
  );
};

export default PieChart;
