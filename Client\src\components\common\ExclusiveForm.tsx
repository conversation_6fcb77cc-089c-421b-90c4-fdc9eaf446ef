import { useState, useEffect } from 'react';
import { use<PERSON><PERSON>, Controller, useFieldArray, Control, FieldErrors, FieldArrayWithId } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
    TextField,
    Button,
    Box,
    Grid,
    Typography,
    IconButton,
    Paper,
    FormHelperText,
    Divider,
    Alert,
    Snackbar,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogContentText,
    DialogTitle,
} from '@mui/material';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { motion } from 'framer-motion';
import { useNavigate, useParams } from 'react-router-dom';
import { useGetIdentity } from '@refinedev/core';
import axios from 'axios';

// Define types
type PaymentPlan = {
    installment: string;
    percentage: number;
    description: string;
};

// --- Optimized Zod Schema for Validation ---
const paymentPlanSchema = z.object({
    installment: z.string().min(1, 'Installment name is required'),
    percentage: z.number().min(0).max(100, 'Percentage cannot exceed 100'),
    description: z.string().optional(),
});

const exclusivePropertyFormSchema = z.object({
    title: z.string().min(3, 'Title must be at least 3 characters').max(100, 'Title must be less than 100 characters'),
    description: z.string().min(10, 'Description must be at least 10 characters').max(1000, 'Description must be less than 1000 characters'),
    propertyType: z.string().min(1, 'Property type is required'),
    location: z.string().min(1, 'Location is required'),
    priceRange: z.object({
        min: z.number().positive('Minimum price must be positive'),
        max: z.number().positive('Maximum price must be positive'),
    }).refine(data => data.max >= data.min, {
        message: "Max price must be greater than min price",
        path: ["max"],
    }),
    typology: z.string().min(1, 'Typology is required'),
    commissionRange: z.object({
        min: z.number().min(0, 'Minimum commission must be at least 0'),
        max: z.number().min(0, 'Maximum commission must be at least 0'),
    }).refine(data => data.max >= data.min, {
        message: "Max commission must be greater than min commission",
        path: ["max"],
    }),
    possessionDate: z.string().refine(val => !isNaN(Date.parse(val)), {
        message: "Invalid date format"
    }),
    configuration: z.string().min(1, 'Configuration is required'),
    reraNumber: z.string().min(1, 'RERA number is required'),
    totalArea: z.number().positive('Total area must be positive'),
    paymentPlan: z.array(paymentPlanSchema).min(1, 'At least one payment plan is required'),
    totalBookings: z.number().int().nonnegative('Total bookings must be a non-negative integer'),
    websiteLink: z.string().url('Must be a valid URL').optional().or(z.literal('')),
    brochureLink: z.string().url('Must be a valid URL').optional().or(z.literal('')),
    gpsLocation: z.string().url('Must be a valid Google Maps URL').optional().or(z.literal('')),
    floorPlanLinks: z.array(z.string().url('Must be a valid URL')).optional(),
    images: z.array(z.string()).min(1, 'At least one image is required'),
    email: z.string().email('Invalid email format')
});

type ExclusivePropertyFormData = z.infer<typeof exclusivePropertyFormSchema>;

// Component Props Interfaces
interface SectionProps {
    control: Control<ExclusivePropertyFormData>;
    errors: FieldErrors<ExclusivePropertyFormData>;
}

interface PaymentPlanSectionProps extends SectionProps {
    fields: FieldArrayWithId<ExclusivePropertyFormData, "paymentPlan", "id">[];
    append: (value: Partial<PaymentPlan>) => void;
    remove: (index: number) => void;
}

interface MediaSectionProps extends SectionProps {
    handleImageUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleImageRemove: (index: number) => void;
    imagePreviewUrls: string[];
}

interface ExclusiveFormProps {
    // No props needed as we're getting the ID from URL parameters
}

const sectionVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4, ease: "easeOut" } },
};

const itemVariants = {
    hidden: { opacity: 0, x: -10 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.3, ease: "easeOut" } },
};

// --- Section Components with proper typing ---
const BasicInfoSection: React.FC<SectionProps> = ({ control, errors }) => (
    <motion.div variants={sectionVariants} initial="hidden" animate="visible">
        <Typography variant="h6" gutterBottom sx={{ mb: 2 }}>Basic Information</Typography>
        <Grid container spacing={3}>
            <Grid item xs={12} component={motion.div} variants={itemVariants}>
                <Controller
                    name="title"
                    control={control}
                    render={({ field }) => (
                        <TextField
                            {...field}
                            label="Property Title"
                            variant="outlined"
                            fullWidth
                            required
                            error={!!errors.title}
                            helperText={errors.title?.message}
                        />
                    )}
                />
            </Grid>
            <Grid item xs={12} component={motion.div} variants={itemVariants}>
                <Controller
                    name="description"
                    control={control}
                    render={({ field }) => (
                        <TextField
                            {...field}
                            label="Description"
                            variant="outlined"
                            fullWidth
                            required
                            multiline
                            rows={4}
                            error={!!errors.description}
                            helperText={errors.description?.message}
                        />
                    )}
                />
            </Grid>
            <Grid item xs={12} sm={6} component={motion.div} variants={itemVariants}>
                <Controller
                    name="propertyType"
                    control={control}
                    render={({ field }) => (
                        <TextField
                            {...field}
                            label="Property Type (e.g., Apartment, Villa)"
                            variant="outlined"
                            fullWidth
                            required
                            error={!!errors.propertyType}
                            helperText={errors.propertyType?.message}
                        />
                    )}
                />
            </Grid>
            <Grid item xs={12} sm={6} component={motion.div} variants={itemVariants}>
                <Controller
                    name="location"
                    control={control}
                    render={({ field }) => (
                        <TextField
                            {...field}
                            label="Location / Address"
                            variant="outlined"
                            fullWidth
                            required
                            error={!!errors.location}
                            helperText={errors.location?.message}
                        />
                    )}
                />
            </Grid>
            <Grid item xs={12} sm={6} component={motion.div} variants={itemVariants}>
                <Controller
                    name="typology"
                    control={control}
                    render={({ field }) => (
                        <TextField
                            {...field}
                            label="Typology (e.g., 1BHK, 2BHK)"
                            variant="outlined"
                            fullWidth
                            required
                            error={!!errors.typology}
                            helperText={errors.typology?.message}
                        />
                    )}
                />
            </Grid>
            <Grid item xs={12} sm={6} component={motion.div} variants={itemVariants}>
                <Controller
                    name="configuration"
                    control={control}
                    render={({ field }) => (
                        <TextField
                            {...field}
                            label="Configuration Details"
                            variant="outlined"
                            fullWidth
                            required
                            error={!!errors.configuration}
                            helperText={errors.configuration?.message}
                        />
                    )}
                />
            </Grid>
            <Grid item xs={12} sm={6} component={motion.div} variants={itemVariants}>
                <Controller
                    name="reraNumber"
                    control={control}
                    render={({ field }) => (
                        <TextField
                            {...field}
                            label="RERA Number"
                            variant="outlined"
                            fullWidth
                            required
                            error={!!errors.reraNumber}
                            helperText={errors.reraNumber?.message}
                        />
                    )}
                />
            </Grid>
            <Grid item xs={12} sm={6} component={motion.div} variants={itemVariants}>
                <Controller
                    name="totalArea"
                    control={control}
                    render={({ field }) => (
                        <TextField
                            {...field}
                            label="Total Area (sq ft)"
                            type="number"
                            variant="outlined"
                            fullWidth
                            required
                            error={!!errors.totalArea}
                            helperText={errors.totalArea?.message}
                            onChange={e => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                    )}
                />
            </Grid>
            <Grid item xs={12} sm={6} component={motion.div} variants={itemVariants}>
                <Controller
                    name="totalBookings"
                    control={control}
                    render={({ field }) => (
                        <TextField
                            {...field}
                            label="Total Bookings"
                            type="number"
                            variant="outlined"
                            fullWidth
                            required
                            error={!!errors.totalBookings}
                            helperText={errors.totalBookings?.message}
                            onChange={e => field.onChange(parseInt(e.target.value) || 0)}
                        />
                    )}
                />
            </Grid>
            <Grid item xs={12} sm={6} component={motion.div} variants={itemVariants}>
                <Controller
                    name="possessionDate"
                    control={control}
                    render={({ field }) => (
                        <TextField
                            {...field}
                            label="Possession Date"
                            type="date"
                            variant="outlined"
                            fullWidth
                            required
                            error={!!errors.possessionDate}
                            helperText={errors.possessionDate?.message || "Expected possession date"}
                            InputLabelProps={{ shrink: true }}
                        />
                    )}
                />
            </Grid>
        </Grid>
    </motion.div>
);

const PricingSection: React.FC<SectionProps> = ({ control, errors }) => (
    <motion.div variants={sectionVariants} initial="hidden" animate="visible">
        <Typography variant="h6" gutterBottom sx={{ mb: 2 }}>Pricing & Commission</Typography>
        <Grid container spacing={3}>
            <Grid item xs={12} sm={6} component={motion.div} variants={itemVariants}>
                <Typography variant="subtitle1" gutterBottom>Price Range</Typography>
                <Grid container spacing={2}>
                    <Grid item xs={6}>
                        <Controller
                            name="priceRange.min"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    label="Min Price"
                                    type="number"
                                    variant="outlined"
                                    fullWidth
                                    required
                                    error={!!errors.priceRange?.min}
                                    helperText={errors.priceRange?.min?.message}
                                    onChange={e => field.onChange(parseFloat(e.target.value) || 0)}
                                />
                            )}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <Controller
                            name="priceRange.max"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    label="Max Price"
                                    type="number"
                                    variant="outlined"
                                    fullWidth
                                    required
                                    error={!!errors.priceRange?.max}
                                    helperText={errors.priceRange?.max?.message}
                                    onChange={e => field.onChange(parseFloat(e.target.value) || 0)}
                                />
                            )}
                        />
                    </Grid>
                </Grid>
            </Grid>
            <Grid item xs={12} sm={6} component={motion.div} variants={itemVariants}>
                <Typography variant="subtitle1" gutterBottom>Commission Range (%)</Typography>
                <Grid container spacing={2}>
                    <Grid item xs={6}>
                        <Controller
                            name="commissionRange.min"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    label="Min Commission"
                                    type="number"
                                    variant="outlined"
                                    fullWidth
                                    required
                                    error={!!errors.commissionRange?.min}
                                    helperText={errors.commissionRange?.min?.message}
                                    onChange={e => field.onChange(parseFloat(e.target.value) || 0)}
                                />
                            )}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <Controller
                            name="commissionRange.max"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    label="Max Commission"
                                    type="number"
                                    variant="outlined"
                                    fullWidth
                                    required
                                    error={!!errors.commissionRange?.max}
                                    helperText={errors.commissionRange?.max?.message}
                                    onChange={e => field.onChange(parseFloat(e.target.value) || 0)}
                                />
                            )}
                        />
                    </Grid>
                </Grid>
            </Grid>
        </Grid>
    </motion.div>
);

const PaymentPlanSection: React.FC<PaymentPlanSectionProps> = ({ control, fields, append, remove, errors }) => (
    <motion.div variants={sectionVariants} initial="hidden" animate="visible">
        <Typography variant="h6" gutterBottom sx={{ mb: 2 }}>Payment Plan</Typography>
        {fields.map((item, index) => (
            <motion.div key={item.id} variants={itemVariants} layout>
                <Paper variant="outlined" sx={{ p: 2, mb: 2, position: 'relative' }}>
                    <Grid container spacing={2} alignItems="center">
                        <Grid item xs={12} sm={4}>
                            <Controller
                                name={`paymentPlan.${index}.installment`}
                                control={control}
                                render={({ field }) => (
                                    <TextField
                                        {...field}
                                        label={`Installment ${index + 1} Name`}
                                        variant="outlined"
                                        fullWidth
                                        required
                                        error={!!errors.paymentPlan?.[index]?.installment}
                                        helperText={errors.paymentPlan?.[index]?.installment?.message}
                                    />
                                )}
                            />
                        </Grid>
                        <Grid item xs={12} sm={3}>
                            <Controller
                                name={`paymentPlan.${index}.percentage`}
                                control={control}
                                render={({ field }) => (
                                    <TextField
                                        {...field}
                                        label="Percentage (%)"
                                        type="number"
                                        variant="outlined"
                                        fullWidth
                                        required
                                        error={!!errors.paymentPlan?.[index]?.percentage}
                                        helperText={errors.paymentPlan?.[index]?.percentage?.message}
                                        onChange={e => field.onChange(parseFloat(e.target.value) || 0)}
                                    />
                                )}
                            />
                        </Grid>
                        <Grid item xs={12} sm={4}>
                            <Controller
                                name={`paymentPlan.${index}.description`}
                                control={control}
                                render={({ field }) => (
                                    <TextField
                                        {...field}
                                        label="Description (Optional)"
                                        variant="outlined"
                                        fullWidth
                                        error={!!errors.paymentPlan?.[index]?.description}
                                        helperText={errors.paymentPlan?.[index]?.description?.message}
                                    />
                                )}
                            />
                        </Grid>
                        <Grid item xs={12} sm={1} sx={{ textAlign: 'right' }}>
                            {fields.length > 1 && (
                                <IconButton
                                    onClick={() => remove(index)}
                                    color="error"
                                    aria-label="Remove payment installment"
                                >
                                    <RemoveCircleOutlineIcon />
                                </IconButton>
                            )}
                        </Grid>
                    </Grid>
                </Paper>
            </motion.div>
        ))}
        {errors.paymentPlan?.root && <FormHelperText error sx={{ ml: 1 }}>{errors.paymentPlan.root.message}</FormHelperText>}
        <Button
            type="button"
            onClick={() => append({ installment: '', percentage: 0, description: '' })}
            startIcon={<AddCircleOutlineIcon />}
            variant="outlined"
            size="small"
            sx={{ mt: 1 }}
        >
            Add Installment
        </Button>
    </motion.div>
);

const MediaSection: React.FC<MediaSectionProps> = ({ control, errors, handleImageUpload, handleImageRemove, imagePreviewUrls }) => (
    <motion.div variants={sectionVariants} initial="hidden" animate="visible">
        <Typography variant="h6" gutterBottom sx={{ mb: 2 }}>Links & Media</Typography>
        <Grid container spacing={3}>
            <Grid item xs={12} sm={6} component={motion.div} variants={itemVariants}>
                <Controller
                    name="websiteLink"
                    control={control}
                    render={({ field }) => (
                        <TextField
                            {...field}
                            label="Website Link (Optional)"
                            variant="outlined"
                            fullWidth
                            error={!!errors.websiteLink}
                            helperText={errors.websiteLink?.message}
                        />
                    )}
                />
            </Grid>
            <Grid item xs={12} sm={6} component={motion.div} variants={itemVariants}>
                <Controller
                    name="brochureLink"
                    control={control}
                    render={({ field }) => (
                        <TextField
                            {...field}
                            label="Brochure Link (Optional)"
                            variant="outlined"
                            fullWidth
                            error={!!errors.brochureLink}
                            helperText={errors.brochureLink?.message}
                        />
                    )}
                />
            </Grid>
            <Grid item xs={12} component={motion.div} variants={itemVariants}>
                <Controller
                    name="gpsLocation"
                    control={control}
                    render={({ field }) => (
                        <TextField
                            {...field}
                            label="GPS Location (Google Maps Link)"
                            variant="outlined"
                            fullWidth
                            error={!!errors.gpsLocation}
                            helperText={errors.gpsLocation?.message || "Enter a Google Maps location link"}
                            placeholder="https://maps.google.com/?q=..."
                        />
                    )}
                />
            </Grid>

            {/* Floor Plan Links */}
            <Grid item xs={12} component={motion.div} variants={itemVariants}>
                <Controller
                    name="floorPlanLinks"
                    control={control}
                    render={({ field }) => (
                        <TextField
                            {...field}
                            label="Floor Plan Links (Comma separated URLs)"
                            variant="outlined"
                            fullWidth
                            error={!!errors.floorPlanLinks}
                            helperText={errors.floorPlanLinks?.message || "Enter comma-separated URLs for floor plans"}
                            onChange={(e) => {
                                const links = e.target.value.split(',').map(link => link.trim());
                                field.onChange(links);
                            }}
                            value={Array.isArray(field.value) ? field.value.join(', ') : ''}
                        />
                    )}
                />
            </Grid>

            {/* Image Upload */}
            <Grid item xs={12} component={motion.div} variants={itemVariants}>
                <Typography variant="subtitle1" gutterBottom>Property Images</Typography>
                <Box sx={{ mb: 2 }}>
                    <Button
                        component="label"
                        variant="outlined"
                        startIcon={<CloudUploadIcon />}
                        sx={{ mb: 2 }}
                    >
                        Upload Images
                        <input
                            type="file"
                            hidden
                            accept="image/*"
                            multiple
                            onChange={handleImageUpload}
                        />
                    </Button>
                    {errors.images && (
                        <FormHelperText error sx={{ ml: 1 }}>
                            {errors.images.message as string}
                        </FormHelperText>
                    )}
                </Box>

                {/* Image Previews */}
                {imagePreviewUrls.length > 0 && (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                        {imagePreviewUrls.map((url, index) => (
                            <Box
                                key={index}
                                sx={{
                                    position: 'relative',
                                    width: 100,
                                    height: 100,
                                    borderRadius: 1,
                                    overflow: 'hidden',
                                }}
                            >
                                <Box
                                    component="img"
                                    src={url}
                                    alt={`Preview ${index + 1}`}
                                    sx={{
                                        width: '100%',
                                        height: '100%',
                                        objectFit: 'cover',
                                    }}
                                />
                                <IconButton
                                    size="small"
                                    onClick={() => handleImageRemove(index)}
                                    sx={{
                                        position: 'absolute',
                                        top: 0,
                                        right: 0,
                                        backgroundColor: 'rgba(255, 0, 0, 0.7)',
                                        color: 'white',
                                        padding: '4px',
                                        zIndex: 10,
                                        '&:hover': {
                                            backgroundColor: 'rgba(255, 0, 0, 0.9)',
                                            transform: 'scale(1.1)',
                                        },
                                    }}
                                    aria-label={`Remove image ${index + 1}`}
                                    title="Remove image"
                                >
                                    <RemoveCircleOutlineIcon fontSize="small" />
                                </IconButton>
                            </Box>
                        ))}
                    </Box>
                )}
            </Grid>
        </Grid>
    </motion.div>
);

const ExclusiveForm: React.FC<ExclusiveFormProps> = () => {
    const navigate = useNavigate();
    const { id } = useParams<{ id: string }>();
    const { data: user } = useGetIdentity<{ email: string, isSuperUser?: boolean }>();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState<string | null>(null);
    const [imagePreviewUrls, setImagePreviewUrls] = useState<string[]>([]);
    const [initialValues, setInitialValues] = useState<Partial<ExclusivePropertyFormData> | null>(null);
    const [isSuperUser, setIsSuperUser] = useState(false);
    const [imageToDelete, setImageToDelete] = useState<number | null>(null);
    const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);

    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:8080';

    // Check if user is a super user
    useEffect(() => {
        const checkSuperUser = async () => {
            if (!user?.email) return;

            try {
                const response = await axios.get(`${apiUrl}/api/v1/exclusive-properties/check-superuser/${user.email}`);
                setIsSuperUser(response.data.isSuperUser);

                if (!response.data.isSuperUser) {
                    setError("Only authorized users can create or edit exclusive properties. Please contact the administrator if you need access.");
                    setTimeout(() => {
                        navigate('/exclusive');
                    }, 3000);
                }
            } catch (err) {
                console.error("Error checking super user status:", err);
                setError("Failed to verify your permissions. Please try again later.");
            }
        };

        checkSuperUser();
    }, [user?.email, navigate, apiUrl]);

    // Fetch property data if editing an existing property
    useEffect(() => {
        const fetchPropertyData = async () => {
            if (!id) return;

            try {
                setLoading(true);
                const response = await axios.get(`${apiUrl}/api/v1/exclusive-properties/${id}`);
                const property = response.data;

                // Ensure all required properties exist and are properly formatted
                const formattedProperty = {
                    title: property.title || '',
                    description: property.description || '',
                    propertyType: property.propertyType || '',
                    location: property.location || '',
                    priceRange: property.priceRange || { min: 0, max: 0 },
                    typology: property.typology || '',
                    commissionRange: property.commissionRange || { min: 0, max: 0 },
                    possessionDate: property.possessionDate
                        ? new Date(property.possessionDate).toISOString().split('T')[0]
                        : new Date().toISOString().split('T')[0],
                    configuration: property.configuration || '',
                    reraNumber: property.reraNumber || '',
                    totalArea: property.totalArea || 0,
                    paymentPlan: Array.isArray(property.paymentPlan) && property.paymentPlan.length > 0
                        ? property.paymentPlan
                        : [{ installment: '', percentage: 0, description: '' }],
                    totalBookings: property.totalBookings || 0,
                    websiteLink: property.websiteLink || '',
                    brochureLink: property.brochureLink || '',
                    gpsLocation: property.gpsLocation || '',
                    floorPlanLinks: Array.isArray(property.floorPlanLinks) ? property.floorPlanLinks : [],
                    images: Array.isArray(property.images) ? property.images : [],
                    email: user?.email || ''
                };



                // Set the formatted data for the form
                setInitialValues(formattedProperty);

                // Set image previews
                setImagePreviewUrls(formattedProperty.images);
            } catch (err: any) {
                console.error("Error fetching property data:", err);
                if (err.response) {
                    console.error("Response status:", err.response.status);
                    console.error("Response data:", err.response.data);
                }
                setError("Failed to load property data. Please try again later.");
            } finally {
                setLoading(false);
            }
        };

        if (id) {
            fetchPropertyData();
        }
    }, [id, apiUrl, user?.email]);

    const {
        control,
        handleSubmit,
        setValue,
        formState: { errors },
    } = useForm<ExclusivePropertyFormData>({
        resolver: zodResolver(exclusivePropertyFormSchema),
        defaultValues: initialValues || {
            title: '',
            description: '',
            propertyType: '',
            location: '',
            priceRange: { min: 0, max: 0 },
            typology: '',
            commissionRange: { min: 0, max: 0 },
            possessionDate: new Date().toISOString().split('T')[0],
            configuration: '',
            reraNumber: '',
            totalArea: 0,
            paymentPlan: [{ installment: '', percentage: 0, description: '' }],
            totalBookings: 0,
            websiteLink: '',
            brochureLink: '',
            gpsLocation: '',
            floorPlanLinks: [''],
            images: [],
            email: user?.email || ''
        }
    });

    // Update form values when initialValues changes
    useEffect(() => {
        if (initialValues) {
            Object.entries(initialValues).forEach(([key, value]) => {
                setValue(key as any, value);
            });
        }
    }, [initialValues, setValue]);

    const { fields: paymentPlanFields, append, remove } = useFieldArray({
        control,
        name: "paymentPlan",
    });

    // Handle image upload
    const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (!e.target.files || e.target.files.length === 0) return;

        const newFiles = Array.from(e.target.files);
        const newPreviewUrls: string[] = [];
        const newImageData: string[] = [];

        let filesProcessed = 0;

        newFiles.forEach(file => {
            const reader = new FileReader();

            reader.onload = () => {
                if (reader.readyState === 2) {
                    const imageData = reader.result as string;
                    newPreviewUrls.push(URL.createObjectURL(file));
                    newImageData.push(imageData);

                    filesProcessed++;

                    // Update form when all files are processed
                    if (filesProcessed === newFiles.length) {
                        setImagePreviewUrls(prev => [...prev, ...newPreviewUrls]);

                        // Get current images from form
                        const currentImages = control._formValues.images || [];

                        // Make sure we're working with an array
                        let currentImagesArray: string[] = [];
                        if (Array.isArray(currentImages)) {
                            currentImagesArray = [...currentImages];
                        } else if (currentImages) {
                            currentImagesArray = [currentImages as string];
                        }

                        const updatedImages = [...currentImagesArray, ...newImageData];

                        // Update form value
                        setValue('images', updatedImages);
                    }
                }
            };

            reader.readAsDataURL(file);
        });
    };

    // Open confirmation dialog for image removal
    const handleImageRemove = (index: number) => {
        setImageToDelete(index);
        setConfirmDialogOpen(true);
    };

    // Confirm and execute image removal
    const confirmImageRemoval = () => {
        const index = imageToDelete;

        if (index === null) {
            setConfirmDialogOpen(false);
            return;
        }

        try {
            // Get the current form values for images
            const currentFormValues = control._formValues;

            // Create a copy of the current images array
            let currentImages: string[] = [];

            // Make sure we're working with an array
            if (currentFormValues && currentFormValues.images) {
                if (Array.isArray(currentFormValues.images)) {
                    currentImages = [...currentFormValues.images];
                } else {
                    currentImages = [currentFormValues.images as string];
                }
            }

            // Remove the image at the specified index
            if (index >= 0 && index < currentImages.length) {
                // If this is an existing Cloudinary image (starts with http), we should track it for deletion
                const imageToRemove = currentImages[index];

                currentImages.splice(index, 1);

                // Update the form value
                setValue('images', currentImages);

                // Update the preview URLs
                const newPreviewUrls = [...imagePreviewUrls];
                newPreviewUrls.splice(index, 1);
                setImagePreviewUrls(newPreviewUrls);
            }
        } catch (error) {
            console.error("Error removing image:", error);
        }

        // Close the dialog and reset the image to delete
        setConfirmDialogOpen(false);
        setImageToDelete(null);
    };

    // Cancel image removal
    const cancelImageRemoval = () => {
        setConfirmDialogOpen(false);
        setImageToDelete(null);
    };

    // Form submission handler
    const onSubmit = async (data: ExclusivePropertyFormData) => {
        if (!isSuperUser) {
            setError("Only authorized users can create or edit exclusive properties. Please contact the administrator if you need access.");
            return;
        }

        // Immediately set loading to true to disable the button
        setLoading(true);
        setError(null);

        try {
            // Validate required fields
            if (!data.title || !data.description || !data.location || !data.propertyType) {
                setError("Please fill in all required fields");
                setLoading(false);
                return;
            }

            // Validate images
            if (!data.images || data.images.length === 0) {
                setError("At least one image is required");
                setLoading(false);
                return;
            }

            // Process floor plan links
            const processedFloorPlanLinks = Array.isArray(data.floorPlanLinks)
                ? data.floorPlanLinks.filter(link => link && link.trim() !== '')
                : [];

            // Ensure all data is properly formatted
            const formData = {
                ...data,
                floorPlanLinks: processedFloorPlanLinks,
                email: user?.email,
                // Ensure these are properly formatted
                priceRange: {
                    min: typeof data.priceRange.min === 'string' ? parseFloat(data.priceRange.min) : data.priceRange.min,
                    max: typeof data.priceRange.max === 'string' ? parseFloat(data.priceRange.max) : data.priceRange.max
                },
                commissionRange: {
                    min: typeof data.commissionRange.min === 'string' ? parseFloat(data.commissionRange.min) : data.commissionRange.min,
                    max: typeof data.commissionRange.max === 'string' ? parseFloat(data.commissionRange.max) : data.commissionRange.max
                },
                totalArea: typeof data.totalArea === 'string' ? parseFloat(data.totalArea) : data.totalArea,
                totalBookings: typeof data.totalBookings === 'string' ? parseInt(data.totalBookings) : data.totalBookings,
                // Ensure images is an array
                images: Array.isArray(data.images) ? data.images : [data.images]
            };

            if (id) {
                // Update existing property
                try {
                    const response = await axios.patch(`${apiUrl}/api/v1/exclusive-properties/${id}`, formData);
                    setSuccess("Property updated successfully!");
                } catch (patchError: any) {
                    if (patchError.response) {
                        throw patchError;
                    } else if (patchError.request) {
                        throw new Error("No response received from server. Please check your connection and try again.");
                    } else {
                        throw patchError;
                    }
                }
            } else {
                // Create new property
                try {
                    const response = await axios.post(`${apiUrl}/api/v1/exclusive-properties`, formData);
                    setSuccess("Property created successfully!");
                } catch (postError: any) {
                    if (postError.response) {
                        throw postError;
                    } else if (postError.request) {
                        throw new Error("No response received from server. Please check your connection and try again.");
                    } else {
                        throw postError;
                    }
                }
            }

            // Redirect after successful submission
            // Keep the button disabled until redirect happens
            setTimeout(() => {
                navigate('/exclusive');
                // We don't set loading to false here because the user will be redirected
            }, 2000);

        } catch (err: any) {
            setError(err.response?.data?.message || "Failed to save property. Please try again later.");
            // Only set loading to false on error, so the button is re-enabled
            setLoading(false);
        }
    };

    if (loading && !initialValues) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
                <CircularProgress />
            </Box>
        );
    }

    return (
        <Paper elevation={3} sx={{ p: { xs: 2, md: 4 }, borderRadius: 2 }}>
            {/* Confirmation Dialog for Image Removal */}
            <Dialog
                open={confirmDialogOpen}
                onClose={cancelImageRemoval}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <DialogTitle id="alert-dialog-title">
                    {"Remove Image?"}
                </DialogTitle>
                <DialogContent>
                    <DialogContentText id="alert-dialog-description">
                        Are you sure you want to remove this image? This action cannot be undone.
                    </DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button onClick={cancelImageRemoval} color="primary">
                        Cancel
                    </Button>
                    <Button onClick={confirmImageRemoval} color="error" autoFocus>
                        Remove
                    </Button>
                </DialogActions>
            </Dialog>

            {error && (
                <Snackbar open={!!error} autoHideDuration={6000} onClose={() => setError(null)}>
                    <Alert severity="error" onClose={() => setError(null)}>
                        {error}
                    </Alert>
                </Snackbar>
            )}

            {success && (
                <Snackbar open={!!success} autoHideDuration={6000} onClose={() => setSuccess(null)}>
                    <Alert severity="success" onClose={() => setSuccess(null)}>
                        {success}
                    </Alert>
                </Snackbar>
            )}

            <Box
                component="form"
                onSubmit={handleSubmit(onSubmit)}
                noValidate
                sx={{ mt: 1 }}
                id="exclusive-property-form"
            >
                <Typography variant="h5" component="h2" gutterBottom sx={{ mb: 3, fontWeight: 'medium' }}>
                    {id ? 'Edit Exclusive Property' : 'Create Exclusive Property'}
                </Typography>

                <BasicInfoSection control={control} errors={errors} />
                <Divider sx={{ my: 4 }} />

                <PricingSection control={control} errors={errors} />
                <Divider sx={{ my: 4 }} />

                <PaymentPlanSection
                    control={control}
                    fields={paymentPlanFields}
                    append={(value) => append(value as any)}
                    remove={remove}
                    errors={errors}
                />
                <Divider sx={{ my: 4 }} />

                <MediaSection
                    control={control}
                    errors={errors}
                    handleImageUpload={handleImageUpload}
                    handleImageRemove={handleImageRemove}
                    imagePreviewUrls={imagePreviewUrls}
                />

                <motion.div variants={sectionVariants} initial="hidden" animate="visible">
                    <Box sx={{ mt: 5, display: 'flex', justifyContent: 'space-between' }}>
                        <Button
                            variant="outlined"
                            onClick={() => navigate('/exclusive')}
                            size="large"
                        >
                            Cancel
                        </Button>
                        <Button
                            type="submit"
                            variant="contained"
                            color="primary"
                            disabled={loading || !isSuperUser}
                            size="large"
                            startIcon={loading ? <CircularProgress size={20} color="inherit" /> : null}
                            onClick={() => {
                                if (!loading && isSuperUser) {
                                    // Set loading to true immediately to disable the button
                                    setLoading(true);
                                    const form = document.getElementById('exclusive-property-form');
                                    if (form) {
                                        form.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
                                    }
                                }
                            }}
                        >
                            {loading ? 'Saving...' : (id ? 'Update Property' : 'Create Property')}
                        </Button>
                    </Box>
                </motion.div>
            </Box>
        </Paper>
    );
};

export default ExclusiveForm;