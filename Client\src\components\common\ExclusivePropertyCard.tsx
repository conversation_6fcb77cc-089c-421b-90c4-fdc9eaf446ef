import { useState } from 'react';
import {
  Box,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Chip,
  Typography,
  Stack,
  alpha,
  useTheme,
  useMediaQuery,
  Grid,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  LocationOn,
  Visibility,
  SquareFoot,
  Apartment,
  Verified,
  Share
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';

// Define the ExclusiveProperty type
export interface ExclusiveProperty {
  _id: string;
  title: string;
  description: string;
  location: string;
  propertyType: string;
  priceRange: {
    min: number;
    max: number;
  };
  typology: string;
  commissionRange: {
    min: number;
    max: number;
  };
  configuration: string;
  totalArea: number;
  gpsLocation?: string;
  images: string[];
  creator: {
    _id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  createdAt: string;
  viewCount?: number;
  formattedPrice?: string;
  formattedCommission?: string;
}

interface ExclusivePropertyCardProps {
  property: ExclusiveProperty;
  index?: number;
  animationDelay?: number;
}

const ExclusivePropertyCard = ({ property, index = 0, animationDelay = 0 }: ExclusivePropertyCardProps) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [isHovered, setIsHovered] = useState(false);

  // Format date to show how recent the property is
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays <= 7) {
      return 'New';
    }
    return null;
  };

  const isNewProperty = formatDate(property.createdAt);

  // Function to handle card click
  const handleCardClick = (e: React.MouseEvent) => {
    // Prevent navigation if clicking on the share button
    if ((e.target as HTMLElement).closest('.share-button')) {
      return;
    }
    navigate(`/exclusive/${property._id}`);
  };

  return (
    <Box
      component={motion.div}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: animationDelay || (index * 0.1) }}
      sx={{
        height: '100%',
        width: '100%'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Card
        elevation={0}
        onClick={handleCardClick}
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          borderRadius: { xs: 2, sm: 3 },
          overflow: 'hidden',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          position: 'relative',
          bgcolor: 'background.paper',
          border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
          '&:hover': {
            transform: isMobile ? 'none' : 'translateY(-8px)',
            boxShadow: '0 20px 40px rgba(0,0,0,0.08)',
            borderColor: alpha(theme.palette.primary.main, 0.2),
          },
          cursor: 'pointer',
          // Touch feedback for mobile
          WebkitTapHighlightColor: 'transparent',
          '&:active': {
            transform: isMobile ? 'scale(0.98)' : 'translateY(-8px)',
            transition: 'transform 0.1s ease',
          },
        }}
      >
        {/* Property Image Section */}
        <Box sx={{ position: 'relative', overflow: 'hidden' }}>
          {/* Exclusive Tag */}
          <Box
            sx={{
              position: 'absolute',
              top: { xs: 8, sm: 16 },
              left: 0,
              zIndex: 2,
              bgcolor: alpha('#000', 0.7),
              color: 'white',
              py: { xs: 0.3, sm: 0.5 },
              px: { xs: 1, sm: 1.5 },
              borderTopRightRadius: { xs: 16, sm: 20 },
              borderBottomRightRadius: { xs: 16, sm: 20 },
              backdropFilter: 'blur(4px)',
              display: 'flex',
              alignItems: 'center',
              gap: 0.5,
              boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
              maxWidth: { xs: '40%', sm: '50%' },
            }}
          >
            <Verified sx={{ fontSize: { xs: 12, sm: 14 }, color: theme.palette.secondary.main }} />
            <Typography
              variant="caption"
              sx={{
                fontWeight: 600,
                letterSpacing: 0.5,
                fontSize: { xs: '0.65rem', sm: '0.7rem' },
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              EXCLUSIVE
            </Typography>
          </Box>

          {/* New Tag - Only shown for new properties */}
          {isNewProperty && (
            <Box
              sx={{
                position: 'absolute',
                top: { xs: 8, sm: 16 },
                right: 0,
                zIndex: 2,
                bgcolor: alpha(theme.palette.success.main, 0.9),
                color: 'white',
                py: { xs: 0.3, sm: 0.5 },
                px: { xs: 1, sm: 1.5 },
                borderTopLeftRadius: { xs: 16, sm: 20 },
                borderBottomLeftRadius: { xs: 16, sm: 20 },
                backdropFilter: 'blur(4px)',
                boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
              }}
            >
              <Typography
                variant="caption"
                sx={{
                  fontWeight: 600,
                  letterSpacing: 0.5,
                  fontSize: { xs: '0.65rem', sm: '0.7rem' }
                }}
              >
                NEW
              </Typography>
            </Box>
          )}

          {/* Property Image */}
          <CardMedia
            component="img"
            height={isMobile ? 160 : 200}
            image={property.images && property.images.length > 0
              ? property.images[0]
              : 'https://via.placeholder.com/400x300?text=No+Image'}
            alt={property.title}
            sx={{
              objectFit: 'cover',
              transition: 'all 0.5s ease',
              transform: !isMobile && isHovered ? 'scale(1.05)' : 'scale(1)',
              filter: !isMobile && isHovered ? 'brightness(1.05) contrast(1.05)' : 'brightness(1) contrast(1)',
              height: { xs: '160px', sm: '180px', md: '200px' }, // Responsive height in sx
            }}
          />

          {/* View Count Badge */}
          <Box
            sx={{
              position: 'absolute',
              bottom: { xs: 8, sm: 16 },
              right: { xs: 8, sm: 16 },
              zIndex: 2,
              display: 'flex',
              alignItems: 'center',
              bgcolor: alpha('#fff', 0.9),
              color: 'text.primary',
              py: { xs: 0.3, sm: 0.5 },
              px: { xs: 0.8, sm: 1 },
              borderRadius: { xs: 16, sm: 20 },
              backdropFilter: 'blur(4px)',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            }}
          >
            <Visibility sx={{ fontSize: { xs: 12, sm: 14 }, mr: 0.5, color: theme.palette.text.secondary }} />
            <Typography
              variant="caption"
              sx={{
                fontWeight: 600,
                color: theme.palette.text.secondary,
                fontSize: { xs: '0.65rem', sm: '0.7rem' }
              }}
            >
              {property.viewCount || 0}
            </Typography>
          </Box>
        </Box>

        {/* Property Details */}
        <CardContent
          sx={{
            p: { xs: 1.5, sm: 2 },
            pb: { xs: 1, sm: 1.5 },
            flexGrow: 1,
            position: 'relative',
          }}
        >
          {/* Property Type & Configuration Tags */}
          <Stack
            direction="row"
            spacing={0.75}
            sx={{
              mb: { xs: 1, sm: 1.5 },
              flexWrap: 'wrap',
              '& > *': {
                mb: { xs: 0.5, sm: 0 }
              }
            }}
          >
            <Chip
              label={property.propertyType}
              size="small"
              sx={{
                bgcolor: alpha(theme.palette.primary.main, 0.1),
                color: theme.palette.primary.main,
                fontWeight: 600,
                fontSize: { xs: '0.65rem', sm: '0.7rem' },
                height: { xs: 22, sm: 24 },
                borderRadius: '4px',
              }}
            />
            <Chip
              label={`${property.configuration} Sq.Ft`}
              size="small"
              sx={{
                bgcolor: alpha(theme.palette.grey[500], 0.1),
                color: theme.palette.text.secondary,
                fontWeight: 600,
                fontSize: { xs: '0.65rem', sm: '0.7rem' },
                height: { xs: 22, sm: 24 },
                borderRadius: '4px',
              }}
            />
          </Stack>

          {/* Title with Tooltip for long titles */}
          <Tooltip title={property.title} placement="top" arrow>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 700,
                mb: { xs: 0.75, sm: 1 },
                fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' },
                display: '-webkit-box',
                WebkitLineClamp: 1,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
                color: theme.palette.text.primary,
                lineHeight: 1.3,
                letterSpacing: '-0.01em',
              }}
            >
              {property.title}
            </Typography>
          </Tooltip>

          {/* Location with Icon */}
          <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: { xs: 1, sm: 1.5 } }}>
            <LocationOn
              sx={{
                color: theme.palette.text.secondary,
                fontSize: { xs: 16, sm: 18 },
                mr: 0.5,
                mt: '2px'
              }}
            />
            <Typography
              variant="body2"
              sx={{
                fontSize: { xs: '0.75rem', sm: '0.8rem', md: '0.85rem' },
                display: '-webkit-box',
                WebkitLineClamp: 1,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
                color: theme.palette.text.secondary,
                lineHeight: 1.4,
              }}
            >
              {property.location}
            </Typography>
          </Box>

          {/* Property Specs Grid */}
          <Grid container spacing={{ xs: 1, sm: 2 }} sx={{ mb: { xs: 1.5, sm: 2 } }}>
            {/* Area */}
            <Grid item xs={6}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <SquareFoot sx={{
                  color: theme.palette.text.secondary,
                  fontSize: { xs: 14, sm: 16 },
                  mr: 0.5
                }} />
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    fontSize: { xs: '0.75rem', sm: '0.8rem' },
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}
                >
                  {property.totalArea} sq.ft
                </Typography>
              </Box>
            </Grid>

            {/* Type */}
            <Grid item xs={6}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Apartment sx={{
                  color: theme.palette.text.secondary,
                  fontSize: { xs: 14, sm: 16 },
                  mr: 0.5
                }} />
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    fontSize: { xs: '0.75rem', sm: '0.8rem' },
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}
                >
                  {property.typology}
                </Typography>
              </Box>
            </Grid>
          </Grid>

          {/* Price Section */}
          <Box
            sx={{
              mb: { xs: 1, sm: 1.5 },
              p: { xs: 1.25, sm: 1.5 },
              borderRadius: { xs: 1.5, sm: 2 },
              bgcolor: alpha(theme.palette.background.default, 0.5),
              border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
            }}
          >
            {/* Price */}
            <Typography
              variant="body1"
              sx={{
                fontWeight: 700,
                fontSize: { xs: '0.95rem', sm: '1.05rem', md: '1.1rem' },
                color: theme.palette.text.primary,
                mb: { xs: 0.75, sm: 1 },
                letterSpacing: '-0.02em',
              }}
            >
              {property.formattedPrice}
            </Typography>

            {/* Commission Badge */}
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                background: `linear-gradient(to right, ${alpha(theme.palette.secondary.light, 0.1)}, ${alpha(theme.palette.secondary.main, 0.05)})`,
                px: { xs: 1.25, sm: 1.5 },
                py: { xs: 0.5, sm: 0.75 },
                borderRadius: { xs: 1, sm: 1.5 },
                border: `1px solid ${alpha(theme.palette.secondary.main, 0.2)}`,
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  color: theme.palette.secondary.main,
                  fontWeight: 700,
                  fontSize: { xs: '0.75rem', sm: '0.8rem', md: '0.85rem' },
                }}
              >
                Commission
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: theme.palette.secondary.dark,
                  fontWeight: 700,
                  fontSize: { xs: '0.75rem', sm: '0.8rem', md: '0.85rem' },
                }}
              >
                {property.formattedCommission}
              </Typography>
            </Box>
          </Box>
        </CardContent>

        {/* Action Area */}
        <CardActions
          sx={{
            p: { xs: 1.5, sm: 2 },
            pt: 0,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}
        >
          {/* Share Button */}
          <Tooltip title="Share Property">
            <IconButton
              className="share-button"
              size="small"
              onClick={(e) => {
                e.stopPropagation(); // Prevent card click event
                // Check if Web Share API is supported
                if (navigator.share) {
                  navigator.share({
                    title: property.title,
                    text: `Check out this exclusive property: ${property.title} in ${property.location}`,
                    url: window.location.origin + `/exclusive/${property._id}`
                  })
                  .catch((error) => console.log('Error sharing:', error));
                } else {
                  // Fallback - copy link to clipboard
                  const url = window.location.origin + `/exclusive/${property._id}`;
                  navigator.clipboard.writeText(url)
                    .then(() => {
                      alert('Link copied to clipboard!');
                    })
                    .catch((error) => {
                      console.error('Failed to copy link:', error);
                    });
                }
              }}
              sx={{
                color: theme.palette.text.secondary,
                '&:hover': {
                  color: theme.palette.primary.main,
                  bgcolor: alpha(theme.palette.primary.main, 0.1)
                },
                transition: 'all 0.2s ease',
                p: { xs: 0.75, sm: 1 }
              }}
            >
              <Share sx={{ fontSize: { xs: 18, sm: 20 } }} />
            </IconButton>
          </Tooltip>

          {/* Clickable Indicator */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              color: theme.palette.text.secondary,
              fontSize: { xs: '0.8rem', sm: '0.85rem' },
              fontWeight: 500,
              opacity: isHovered ? 1 : 0.7,
              transition: 'all 0.2s ease',
            }}
          >
            <Typography
              variant="body2"
              sx={{
                mr: 0.5,
                display: { xs: isMobile ? 'none' : 'block', sm: 'block' } // Hide text on very small mobile screens
              }}
            >
              View Details
            </Typography>
            <Box
              component="span"
              sx={{
                width: { xs: 14, sm: 16 },
                height: { xs: 14, sm: 16 },
                borderRadius: '50%',
                bgcolor: alpha(theme.palette.primary.main, 0.1),
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                transform: isHovered ? 'translateX(4px)' : 'translateX(0)',
                transition: 'transform 0.2s ease',
              }}
            >
              <Box
                component="span"
                sx={{
                  width: { xs: 5, sm: 6 },
                  height: { xs: 5, sm: 6 },
                  borderRadius: '50%',
                  bgcolor: theme.palette.primary.main,
                }}
              />
            </Box>
          </Box>
        </CardActions>
      </Card>
    </Box>
  );
};

export default ExclusivePropertyCard;
