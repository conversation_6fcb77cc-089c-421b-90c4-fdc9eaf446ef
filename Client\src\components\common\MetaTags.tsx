import React, { useEffect } from 'react';

interface MetaTagsProps {
  title: string;
  description: string;
  imageUrl: string | undefined;
  url: string;
  type?: string;
  siteName?: string;
  twitterCard?: string;
  price?: string | undefined;
  location?: string;
  propertyType?: string;
}

/**
 * MetaTags component for optimizing social media sharing
 *
 * This component dynamically updates meta tags in the document head
 * to provide rich previews when content is shared on social media platforms.
 */
const MetaTags: React.FC<MetaTagsProps> = ({
  title,
  description,
  imageUrl,
  url,
  type = 'website',
  siteName = 'BrickBix',
  twitterCard = 'summary_large_image',
  price,
  location,
  propertyType,
}) => {
  useEffect(() => {
    // Store original meta tags to restore later
    const originalTitle = document.title;
    const metaTags: HTMLMetaElement[] = [];

    // Helper function to create or update meta tags
    const setMetaTag = (name: string, content: string, property?: string) => {
      // Try to find existing tag
      let meta: HTMLMetaElement | null = null;

      if (property) {
        meta = document.querySelector(`meta[property="${property}"]`);
      } else if (name) {
        meta = document.querySelector(`meta[name="${name}"]`);
      }

      // If tag doesn't exist, create it
      if (!meta) {
        meta = document.createElement('meta');
        if (property) {
          meta.setAttribute('property', property);
        } else {
          meta.setAttribute('name', name);
        }
        document.head.appendChild(meta);
        metaTags.push(meta);
      }

      // Set content
      meta.setAttribute('content', content);
    };

    // Set page title with property name and location for better SEO
    const formattedTitle = propertyType && location
      ? `${title} - ${propertyType} in ${location} | ${siteName}`
      : `${title} | ${siteName}`;

    document.title = formattedTitle;

    // Enhanced description with price if available
    const enhancedDescription = price
      ? `${description} Price: ${price}`
      : description;

    // Set basic meta tags
    setMetaTag('description', enhancedDescription);

    // Set Open Graph meta tags
    setMetaTag('og:title', formattedTitle, 'og:title');
    setMetaTag('og:description', enhancedDescription, 'og:description');
    if (imageUrl) {
      setMetaTag('og:image', imageUrl, 'og:image');
    }
    setMetaTag('og:url', url, 'og:url');
    setMetaTag('og:type', type, 'og:type');
    setMetaTag('og:site_name', siteName, 'og:site_name');

    // Add additional property-specific Open Graph tags
    if (price) {
      setMetaTag('og:price:amount', price.replace(/[^0-9.]/g, ''), 'og:price:amount');
      setMetaTag('og:price:currency', 'INR', 'og:price:currency');
    }

    if (location) {
      setMetaTag('og:locality', location, 'og:locality');
    }

    // Set Twitter meta tags
    setMetaTag('twitter:card', twitterCard);
    setMetaTag('twitter:title', formattedTitle);
    setMetaTag('twitter:description', enhancedDescription);
    if (imageUrl) {
      setMetaTag('twitter:image', imageUrl);
    }
    setMetaTag('twitter:site', '@BrickBix');

    // Add structured data for better SEO
    const structuredData = {
      '@context': 'https://schema.org',
      '@type': 'RealEstateListing',
      'name': title,
      'description': description,
      ...(imageUrl && { 'image': imageUrl }),
      'url': url,
      ...(price && { 'offers': {
        '@type': 'Offer',
        'price': price.replace(/[^0-9.]/g, ''),
        'priceCurrency': 'INR'
      }}),
      ...(location && { 'address': {
        '@type': 'PostalAddress',
        'addressLocality': location
      }}),
      ...(propertyType && { 'propertyType': propertyType })
    };

    // Add structured data script tag
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.text = JSON.stringify(structuredData);
    document.head.appendChild(script);

    // Cleanup function to restore original meta tags
    return () => {
      document.title = originalTitle;
      metaTags.forEach(tag => {
        if (tag.parentNode) {
          tag.parentNode.removeChild(tag);
        }
      });

      // Remove structured data script
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, [title, description, imageUrl, url, type, siteName, twitterCard, price, location, propertyType]);

  // This component doesn't render anything
  return null;
};

export default MetaTags;
