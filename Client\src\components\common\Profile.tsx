import React, { useState } from 'react';
import {
    <PERSON>,
    Typography,
    Stack,
    Tabs,
    Tab,
    Button,
    Dialog,
    Avatar,
    Paper,
    Container,
    useTheme,
    useMediaQuery
} from '@mui/material';
import {
    Email,
    Phone,
    Place,
    Edit,
    Business,
    ArrowBack
} from '@mui/icons-material';
import { ProfileProps, PropertyProps } from "../../interfaces/common";
import PropertyCard from "./PropertyCard";
import BrickBixImage from '../../assets/brick bix image.jpg';
import UserForm from "./UserForm";
import { useNavigate } from "react-router-dom";

interface ExtendedProfileProps extends ProfileProps {
    currentUserId?: string;
    profileUserId?: string;
    services?: {
        buySell: boolean;
        rent: boolean;
        commercial: boolean;
        residential: boolean;
    };
    reraNumber?: string;
    isOwnProfile?: boolean;
}

const ProfileHeader = ({ type, isOwnProfile, onBackClick }: { type: string; isOwnProfile: boolean; onBackClick?: () => void }) => (
    <Box sx={{ display: 'flex', alignItems: 'center', mb: 4, position: 'relative' }}>
        {!isOwnProfile && (
            <Button 
                startIcon={<ArrowBack />} 
                onClick={onBackClick} 
                sx={{ position: 'absolute', left: 0 }}
            >
                Back
            </Button>
        )}
        <Typography
            variant="h4"
            sx={{
                fontWeight: 700,
                fontSize: { xs: '1.5rem', sm: '1.75rem', md: '2rem' },
                textAlign: 'center',
                width: '100%'
            }}
        >
            <span style={{ color: '#d84030' }}>{isOwnProfile ? 'My' : 'Agent'}</span>
            <span style={{ color: '#11418a' }}> Profile</span>
        </Typography>
    </Box>
);

const ContactInfo = ({ icon: Icon, text }: { icon: React.ElementType; text: string }) => (
    <Stack
        direction="row"
        alignItems="center"
        spacing={1}
        sx={{
            backgroundColor: 'rgba(0, 0, 0, 0.04)',
            borderRadius: 2,
            padding: 1
        }}
    >
        <Icon sx={{ color: "#11418a" }} />
        <Typography fontSize={14} color="#11142D">
            {text}
        </Typography>
    </Stack>
);

const TabPanel = ({ children, value, index }: { children: React.ReactNode; value: number; index: number }) => (
    <Box hidden={value !== index} sx={{ p: 3 }}>
        {value === index && children}
    </Box>
);

const Profile = ({
    type,
    name,
    avatar,
    email,
    phone,
    workLocation,
    properties,
    requirement,
    currentUserId,
    profileUserId,
    services,
    reraNumber,
    isOwnProfile = false
}: ExtendedProfileProps) => {
    const [selectedTab, setSelectedTab] = useState(0);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const navigate = useNavigate();

    // Determine if we are viewing our own profile
    const isProfileOwner = isOwnProfile || currentUserId === profileUserId;

    const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
        setSelectedTab(newValue);
    };

    const defaultAvatar = "https://upload.wikimedia.org/wikipedia/commons/thumb/5/59/User-avatar.svg/2048px-User-avatar.svg.png";

    const handleFormSubmit = (success: boolean) => {
        if (!success) {
            setIsEditDialogOpen(true); // Keep dialog open to show errors
        }
        // If success is true, page will be reloaded in UserForm after thank you message
    };
    
    const handleBackClick = () => {
        navigate(-1);
    };

    // Filter active services
    const activeServices = services ? 
        Object.entries(services).filter(([_, value]) => value === true) : [];
    const hasServices = activeServices.length > 0;

    return (
        <Container maxWidth="lg" sx={{ py: 4 }}>
            <ProfileHeader type={type} isOwnProfile={isProfileOwner} onBackClick={handleBackClick} />

            <Paper
                elevation={2}
                sx={{
                    borderRadius: 4,
                    overflow: 'hidden',
                    mb: 4
                }}
            >
                <Box sx={{ p: { xs: 2, sm: 3, md: 4 } }}>
                    <Stack
                        direction={{ xs: 'column', md: 'row' }}
                        spacing={4}
                        alignItems="center"
                    >
                        <Avatar
                            src={avatar || defaultAvatar}
                            sx={{
                                width: 120,
                                height: 120,
                                border: 3,
                                borderColor: '#11418a'
                            }}
                        />

                        <Box flex={1}>
                            <Stack spacing={2}>
                                <Box sx={{
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    flexWrap: 'wrap',
                                    gap: 2
                                }}>
                                    <Typography
                                        variant="h5"
                                        fontWeight={600}
                                        color="#11142D"
                                    >
                                        {name}
                                    </Typography>
                                    {isProfileOwner && (
                                        <Button
                                            variant="contained"
                                            onClick={() => setIsEditDialogOpen(true)}
                                            startIcon={<Edit />}
                                            sx={{
                                                borderRadius: "20px",
                                                backgroundColor: '#11418a',
                                                '&:hover': {
                                                    backgroundColor: '#0d3166'
                                                }
                                            }}
                                        >
                                            Edit Profile
                                        </Button>
                                    )}
                                </Box>

                                <Stack direction="row" alignItems="center" spacing={1}>
                                    <Business sx={{ color: "#808191" }} />
                                    <Typography color="#808191">
                                        Real Estate Agent
                                        {reraNumber && ` • RERA: ${reraNumber}`}
                                    </Typography>
                                </Stack>

                                <Stack
                                    direction={{ xs: 'column', sm: 'row' }}
                                    spacing={2}
                                    sx={{ mt: 2 }}
                                >    
                                    {workLocation && <ContactInfo icon={Place} text={workLocation} />}
                                    {phone && <ContactInfo icon={Phone} text={phone} />}
                                    {email && <ContactInfo icon={Email} text={email} />}
                                </Stack>
                                
                                {/* Services display */}
                                {hasServices && (
                                    <Box sx={{ mt: 2 }}>
                                        <Typography variant="subtitle2" color="#11142D" sx={{ mb: 1 }}>
                                            Services Offered:
                                        </Typography>
                                        <Stack direction="row" flexWrap="wrap" gap={1}>
                                            {services?.buySell && (
                                                <Box sx={{ 
                                                    backgroundColor: 'rgba(25, 118, 210, 0.08)', 
                                                    borderRadius: 1, 
                                                    px: 1, 
                                                    py: 0.5,
                                                    color: '#1976d2',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    gap: 0.5,
                                                    fontSize: '0.85rem'
                                                }}>
                                                    Buy/Sell
                                                </Box>
                                            )}
                                            {services?.rent && (
                                                <Box sx={{ 
                                                    backgroundColor: 'rgba(25, 118, 210, 0.08)', 
                                                    borderRadius: 1, 
                                                    px: 1, 
                                                    py: 0.5,
                                                    color: '#1976d2',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    gap: 0.5,
                                                    fontSize: '0.85rem'
                                                }}>
                                                    Rent
                                                </Box>
                                            )}
                                            {services?.commercial && (
                                                <Box sx={{ 
                                                    backgroundColor: 'rgba(25, 118, 210, 0.08)', 
                                                    borderRadius: 1, 
                                                    px: 1, 
                                                    py: 0.5,
                                                    color: '#1976d2',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    gap: 0.5,
                                                    fontSize: '0.85rem'
                                                }}>
                                                    Commercial
                                                </Box>
                                            )}
                                            {services?.residential && (
                                                <Box sx={{ 
                                                    backgroundColor: 'rgba(25, 118, 210, 0.08)', 
                                                    borderRadius: 1, 
                                                    px: 1, 
                                                    py: 0.5,
                                                    color: '#1976d2',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    gap: 0.5,
                                                    fontSize: '0.85rem'
                                                }}>
                                                    Residential
                                                </Box>
                                            )}
                                        </Stack>
                                    </Box>
                                )}
                            </Stack>
                        </Box>
                    </Stack>
                </Box>

                <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                    <Tabs
                        value={selectedTab}
                        onChange={handleTabChange}
                        variant={isMobile ? "fullWidth" : "standard"}
                        sx={{
                            '& .MuiTab-root': {
                                fontSize: { xs: '0.875rem', sm: '1rem' },
                                minWidth: { xs: 'auto', sm: 160 }
                            }
                        }}
                    >
                        <Tab label={`Properties (${properties?.length || 0})`} />
                        <Tab label={`Requirements (${requirement?.length || 0})`} />
                    </Tabs>
                </Box>

                <TabPanel value={selectedTab} index={0}>
                    <Box
                        sx={{
                            display: "grid",
                            gridTemplateColumns: {
                                xs: '1fr',
                                sm: 'repeat(2, 1fr)',
                                md: 'repeat(3, 1fr)'
                            },
                            gap: 3
                        }}
                    >
                        {properties && properties.length > 0 ? (
                            properties.map((property: PropertyProps) => {
                             
                                return (
                                    //@ts-ignore
                                    <PropertyCard
                                      key={property._id}
                                      id={property._id}
                                      title={property.title}
                                      location={property.location}
                                      //@ts-ignore
                                      dealType={property.dealType}
                                      price={property.price}
                                      //@ts-ignore
                                      photo={property.photo || property.photos}
                                      //@ts-ignore
                                      phone={property.phone}
                                      //@ts-ignore
                                      propertyType={property.propertyType}
                                      url='properties'
                                  />
                                )
                            })
                        ) : (
                            <Typography color="text.secondary" sx={{ gridColumn: '1/-1', textAlign: 'center' }}>
                                No properties found.
                            </Typography>
                        )}
                    </Box>
                </TabPanel>

                <TabPanel value={selectedTab} index={1}>
                    <Box
                        sx={{
                            display: "grid",
                            gridTemplateColumns: {
                                xs: '1fr',
                                sm: 'repeat(2, 1fr)',
                                md: 'repeat(3, 1fr)'
                            },
                            gap: 3
                        }}
                    >
                        {requirement && requirement.length > 0 ? (
                            requirement.map((item: any) => (
                                <Paper
                                    key={item._id}
                                    elevation={1}
                                    sx={{
                                        p: 2,
                                        borderRadius: 2,
                                        '&:hover': {
                                            boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                                            transform: 'translateY(-2px)',
                                            transition: 'all 0.3s'
                                        }
                                    }}
                                    component="a"
                                    href={`/properties-requirement/show/${item._id}`}
                                    //@ts-ignore
                                    style={{ textDecoration: 'none' }}
                                >
                                    <Typography
                                        variant="subtitle1"
                                        fontWeight={600}
                                        color="#11142D"
                                        gutterBottom
                                        noWrap
                                    >
                                        {item.title}
                                    </Typography>
                                    <Typography
                                        variant="body2"
                                        color="text.secondary"
                                        gutterBottom
                                    >
                                        Location: {item.location}
                                    </Typography>
                                    <Typography
                                        variant="body2"
                                        color="text.secondary"
                                    >
                                        Budget: ₹{item.minPrice} - ₹{item.maxPrice}
                                    </Typography>
                                </Paper>
                            ))
                        ) : (
                            <Typography color="text.secondary" sx={{ gridColumn: '1/-1', textAlign: 'center' }}>
                                No requirements found.
                            </Typography>
                        )}
                    </Box>
                </TabPanel>
            </Paper>

            {/* Edit Profile Dialog */}
            <Dialog
                open={isEditDialogOpen}
                onClose={() => setIsEditDialogOpen(false)}
                fullWidth
                maxWidth="md"
            >
                <Box sx={{ p: { xs: 2, md: 4 } }}>
                    <Typography
                        variant="h5"
                        fontWeight={600}
                        color="#11142D"
                        gutterBottom
                    >
                        Edit Profile
                    </Typography>
                    <UserForm 
                        name={name}
                        email={email}
                        phone={phone}
                        workLocation={workLocation || ''}
                        onFormSubmit={handleFormSubmit}
                    />
                </Box>
            </Dialog>
        </Container>
    );
};

export default Profile;