import Place from "@mui/icons-material/Place";
import { Link } from "react-router-dom";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import Card from "@mui/material/Card";
import CardMedia from "@mui/material/CardMedia";
import CardContent from "@mui/material/CardContent";
import Stack from "@mui/material/Stack";
import { motion } from "framer-motion";
import { alpha, Theme } from "@mui/material/styles";
import { PropertyCardProps } from "../../interfaces/property";
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import { useState } from "react";
import Skeleton from "@mui/material/Skeleton";
import { useTheme } from "@mui/material/styles";

const MotionBox = motion(Box);

const PropertyCard = ({
  id,
  title,
  location,
  price,
  photo,
  propertyType,
  dealType,
  phone,
  url,
  previousPrice,
}: PropertyCardProps) => {
  const theme = useTheme();
  const [isImageLoading, setIsImageLoading] = useState(true);
  const [isImageError, setIsImageError] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  let displayPhoto: string = "";

  if (typeof photo === 'string') {
    displayPhoto = photo;
  } else if (Array.isArray(photo) && photo.length > 0) {
    //@ts-ignore
    displayPhoto = photo[0];
  }

  const handleImageLoad = () => {
    setIsImageLoading(false);
  };

  const handleImageError = () => {
    setIsImageLoading(false);
    setIsImageError(true);
  };

  const priceChange = previousPrice ? ((parseFloat(price) - parseFloat(previousPrice)) / parseFloat(previousPrice)) * 100 : 0;

  return (
    <Box
      component={Link}
      to={`/${url}/show/${id}`}
      sx={{ 
        textDecoration: "none",
        width: "100%",
        maxWidth: {
          xs: "100%",
          sm: "calc(50% - 16px)",
          md: "calc(33.33% - 16px)",
          lg: "calc(25% - 16px)",
          xl: "calc(20% - 16px)",
        },
        minWidth: {
          xs: "280px",
          sm: "300px",
        },
      }}
    >
      <MotionBox
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        whileHover={{ 
          y: -5,
          transition: { duration: 0.2 }
        }}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
        sx={{
          height: "100%",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <Card
          sx={{
            width: "100%",
            height: "100%",
            cursor: "pointer",
            backgroundColor: "#ffffff",
            borderRadius: "24px",
            position: "relative",
            overflow: "hidden",
            boxShadow: (theme: Theme) => `0 4px 20px ${alpha(theme.palette.common.black, 0.08)}`,
            transition: "all 0.3s ease-in-out",
            display: "flex",
            flexDirection: "column",
            "&:hover": {
              boxShadow: (theme: Theme) => `0 8px 30px ${alpha(theme.palette.common.black, 0.12)}`,
            },
          }}
          elevation={0}
        >
          {/* Deal Type Badge */}
          <Box
            sx={{
              position: "absolute",
              top: 16,
              left: 16,
              backgroundColor: (theme: Theme) => alpha(theme.palette.primary.main, 0.9),
              color: "#ffffff",
              padding: {
                xs: "4px 8px",
                sm: "6px 12px",
              },
              borderRadius: "12px",
              zIndex: 1,
              backdropFilter: "blur(4px)",
            }}
          >
            <Typography 
              variant="body2" 
              sx={{ 
                fontSize: {
                  xs: "0.7rem",
                  sm: "0.75rem",
                  md: "0.8rem",
                },
                fontWeight: 600,
                letterSpacing: "0.5px"
              }}
            >
              {dealType}
            </Typography>
          </Box>

          {/* Image Container with Overlay */}
          <Box sx={{ position: "relative", flex: "0 0 auto" }}>
            {isImageLoading && (
              <Box sx={{ position: "relative", width: "100%", paddingTop: "56.25%" }}>
                <Skeleton
                  variant="rectangular"
                  width="100%"
                  height="100%"
                  animation="wave"
                  sx={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    borderRadius: "24px 24px 0 0",
                  }}
                />
              </Box>
            )}
            <CardMedia
              sx={{
                borderRadius: "24px 24px 0 0",
                width: "100%",
                aspectRatio: "16/9",
                objectFit: 'cover',
                transition: "transform 0.3s ease-in-out",
                display: isImageLoading ? 'none' : 'block',
                "&:hover": {
                  transform: "scale(1.05)",
                },
              }}
              component="img"
              image={isImageError ? "/placeholder-image.jpg" : displayPhoto}
              alt={title}
              onLoad={handleImageLoad}
              onError={handleImageError}
            />
            <Box
              sx={{
                position: "absolute",
                bottom: 0,
                left: 0,
                right: 0,
                height: "50%",
                background: "linear-gradient(to top, rgba(0,0,0,0.7), transparent)",
                opacity: isHovered ? 1 : 0,
                transition: "opacity 0.3s ease-in-out",
              }}
            />
          </Box>

          <CardContent
            sx={{
              padding: {
                xs: "16px",
                sm: "20px",
                md: "24px",
              },
              display: "flex",
              flexDirection: "column",
              gap: {
                xs: "12px",
                sm: "16px",
              },
              flex: "1 1 auto",
            }}
          >
            {isImageLoading ? (
              <>
                <Stack direction="column" spacing={1.5}>
                  <Skeleton
                    variant="text"
                    width="80%"
                    height={24}
                    animation="wave"
                  />
                  <Stack direction="row" spacing={1} alignItems="center">
                    <Skeleton
                      variant="circular"
                      width={20}
                      height={20}
                      animation="wave"
                    />
                    <Skeleton
                      variant="text"
                      width="60%"
                      height={20}
                      animation="wave"
                    />
                  </Stack>
                </Stack>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    mt: "auto",
                  }}
                >
                  <Box>
                    <Skeleton
                      variant="text"
                      width={120}
                      height={32}
                      animation="wave"
                    />
                    <Skeleton
                      variant="text"
                      width={80}
                      height={20}
                      animation="wave"
                    />
                  </Box>
                  <Skeleton
                    variant="rectangular"
                    width={80}
                    height={32}
                    animation="wave"
                    sx={{ borderRadius: "12px" }}
                  />
                </Box>
              </>
            ) : (
              <>
                <Stack direction="column" gap={1.5}>
                  <Typography
                    variant="h6"
                    sx={{
                      fontSize: {
                        xs: "0.875rem",
                        sm: "1rem",
                        md: "1.125rem",
                        lg: "1.1rem",
                      },
                      fontWeight: 700,
                      color: "text.primary",
                      lineHeight: 1.4,
                      display: "-webkit-box",
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: "vertical",
                      overflow: "hidden",
                    }}
                  >
                    {title}
                  </Typography>
                  
                  <Stack direction="row" spacing={1} alignItems="center">
                    <Place sx={{ 
                      fontSize: {
                        xs: 16,
                        sm: 18,
                        md: 18,
                      },
                      color: "primary.main" 
                    }} />
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{
                        fontSize: {
                          xs: "0.75rem",
                          sm: "0.875rem",
                          md: "0.9rem",
                        },
                        fontWeight: 500,
                        display: "-webkit-box",
                        WebkitLineClamp: 1,
                        WebkitBoxOrient: "vertical",
                        overflow: "hidden",
                      }}
                    >
                      {location}
                    </Typography>
                  </Stack>
                </Stack>

                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    mt: "auto",
                  }}
                >
                  <Box>
                    <Typography
                      variant="h5"
                      sx={{
                        fontSize: {
                          xs: "1rem",
                          sm: "1.25rem",
                          md: "1.3rem",
                        },
                        fontWeight: 700,
                        color: "primary.main",
                      }}
                    >
                      ₹ {new Intl.NumberFormat("en-IN").format(parseFloat(price))}/-
                    </Typography>
                    {previousPrice && (
                      <Stack direction="row" spacing={0.5} alignItems="center">
                        {priceChange > 0 ? (
                          <TrendingUpIcon sx={{ 
                            color: "success.main", 
                            fontSize: {
                              xs: 14,
                              sm: 16,
                            }
                          }} />
                        ) : (
                          <TrendingDownIcon sx={{ 
                            color: "error.main", 
                            fontSize: {
                              xs: 14,
                              sm: 16,
                            }
                          }} />
                        )}
                        <Typography
                          variant="caption"
                          sx={{
                            color: priceChange > 0 ? "success.main" : "error.main",
                            fontWeight: 500,
                            fontSize: {
                              xs: "0.7rem",
                              sm: "0.75rem",
                            }
                          }}
                        >
                          {Math.abs(priceChange).toFixed(1)}%
                        </Typography>
                      </Stack>
                    )}
                  </Box>
                  
                  <Box
                    sx={{
                      px: {
                        xs: 1.5,
                        sm: 2,
                      },
                      py: {
                        xs: 0.5,
                        sm: 1,
                      },
                      borderRadius: "12px",
                      backgroundColor: (theme: Theme) => alpha(theme.palette.primary.main, 0.1),
                    }}
                  >
                    <Typography
                      sx={{
                        fontSize: {
                          xs: "0.75rem",
                          sm: "0.875rem",
                          md: "0.9rem",
                        },
                        fontWeight: 600,
                        color: "primary.main",
                      }}
                    >
                      {propertyType}
                    </Typography>
                  </Box>
                </Box>
              </>
            )}
          </CardContent>
        </Card>
      </MotionBox>
    </Box>
  );
};

export default PropertyCard;