import Box from "@mui/material/Box"; 
import Typography from "@mui/material/Typography";
import FormControl from "@mui/material/FormControl";
import FormHelperText from "@mui/material/FormHelperText";
import TextField from "@mui/material/TextField";
import TextareaAutosize from "@mui/material/TextareaAutosize";
import Stack from "@mui/material/Stack";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import { RequirementFormProps } from "../../interfaces/common";
import CustomButton from "./CustomButton";
import { useState } from "react";


const RequirementForm = ({
  type,
  register,
  handleSubmit,
  formLoading,
  onFinishHandler,
}: RequirementFormProps) => {
  const [isButtonDisabled, setIsButtonDisabled] = useState(false);

  // Create a wrapper for the form submission
  const handleFormSubmit = (data: any) => {
    // Clean and format the data
    const cleanedData = {
      ...data,
      title: data.title?.trim(),
      description: data.description?.trim(),
      location: data.location?.trim().toLowerCase(),
      propertyType: data.propertyType?.trim().toLowerCase(),
      dealType: data.dealType?.trim(),
      phone: data.phone?.trim(),
      askedPrice: data.askedPrice // Keep numeric value as is
    };

    // Call the original handler with cleaned data
    onFinishHandler(cleanedData);
  };

  const handleClick = () => {
    // Update state to disable the button
    setIsButtonDisabled(true);

    // Perform your desired action here
    // For example, make an API call, perform validation, etc.
    // Once the action is completed, you can re-enable the button if needed
  };

  return (
    <Box
      sx={{
        width: { xs: "100%", sm: "80%", md: "60%" },
        mx: "auto",
        p: 4,
        borderRadius: 2,
        boxShadow: 3,
        backgroundColor: "#fff",
      }}
    >
      <Typography
        sx={{ fontWeight: "bold", fontSize: 24, mb: 3, color: "primary.main" }}
        variant="h5"
        textAlign="center"
      >
        <span style={{ color: "#d84030" }}>{type}</span> Requirement!
      </Typography>

      <Box component="form" onSubmit={handleSubmit(handleFormSubmit)}>
        <Stack spacing={3}>
          <FormControl>
            <TextField
              fullWidth
              label="Enter Requirement Title"
              required
              color="info"
              variant="outlined"
              {...register("title", { 
                required: true,
                setValueAs: (value: string) => value?.trim() 
              })}
            />
          </FormControl>

          <FormControl>
            <TextareaAutosize
              minRows={5}
              required
              placeholder="Enter description"
              style={{
                width: "100%",
                background: "transparent",
                fontSize: "16px",
                borderColor: "rgba(0,0,0,0.23)",
                borderRadius: 6,
                padding: 10,
                color: "#000",
              }}
              {...register("description", { 
                required: true,
                setValueAs: (value: string) => value?.trim()
              })}
            />
          </FormControl>

          <Stack direction={{ xs: "column", sm: "row" }} spacing={2}>
            <FormControl sx={{ flex: 1 }}>
              <FormHelperText
                sx={{
                  fontWeight: 500,
                  mb: 1,
                  fontSize: 16,
                  color: "#11142d",
                }}
              >
                Select Requirement Type
              </FormHelperText>
              <Select
                variant="outlined"
                color="info"
                displayEmpty
                required
                defaultValue="apartment"
                {...register("propertyType", {
                  required: true,
                  setValueAs: (value: string) => value?.toLowerCase().trim()
                })}
              >
                <MenuItem value="apartment">Apartment</MenuItem>
                <MenuItem value="rental">Rental</MenuItem>
                <MenuItem value="commercial">Commercial</MenuItem>
                <MenuItem value="farmhouse">Farmhouse</MenuItem>
                <MenuItem value="duplex">Duplex</MenuItem>
                <MenuItem value="plot">Plot</MenuItem>
                <MenuItem value="land">Land</MenuItem>
                <MenuItem value="room">Room</MenuItem>
              </Select>
            </FormControl>

            <FormControl sx={{ flex: 1 }}>
              <FormHelperText
                sx={{
                  fontWeight: 500,
                  mb: 1,
                  fontSize: 16,
                  color: "#11142d",
                }}
              >
                Deal Type
              </FormHelperText>
              <Select
                variant="outlined"
                color="info"
                displayEmpty
                required
                defaultValue="Direct"
                {...register("dealType", { 
                  required: true,
                  setValueAs: (value: string) => value?.trim()
                })}
              >
                <MenuItem value="Direct">Direct</MenuItem>
                <MenuItem value="Indirect">Indirect</MenuItem>
              </Select>
            </FormControl>
          </Stack>

          <FormControl>
            <TextField
              fullWidth
              required
              label="Enter Budget"
              color="info"
              type="number"
              variant="outlined"
              {...register("askedPrice", {
                required: "Budget is required",
                min: { value: 0, message: "Budget must be a positive number" },
                max: {
                  value: 1000000000,
                  message: "Budget cannot exceed 1,000,000,000",
                },
                pattern: {
                  value: /^\d+$/,
                  message: "Please enter a valid number",
                },
              })}
            />
          </FormControl>

          <FormControl>
            <Box display="flex" alignItems="center">
              <TextField
                value="+91"
                disabled
                sx={{
                  maxWidth: "70px",
                  "& .MuiInputBase-root": {
                    backgroundColor: "#f0f0f0",
                  },
                }}
              />
              <TextField
                fullWidth
                required
                label="Enter Phone Number"
                color="info"
                type="tel"
                variant="outlined"
                inputProps={{
                  pattern: "[0-9]{10}",
                  title: "Please enter a valid 10-digit mobile number",
                }}
                sx={{ ml: 2 }}
                {...register("phone", {
                  required: true,
                  pattern: /[0-9]{10}/,
                  setValueAs: (value: string) => value?.trim()
                })}
              />
            </Box>
          </FormControl>

          <FormControl>
            <TextField
              fullWidth
              required
              label="Enter Location"
              color="info"
              variant="outlined"
              {...register("location", { 
                required: true,
                setValueAs: (value: string) => value?.trim().toLowerCase()
              })}
            />
          </FormControl>
          
          <CustomButton
            type="submit"
            title={formLoading ? "Submitting..." : "Submit"}
            backgroundColor="#0F52BA"
            color="#fcfcfc"
            handleClick={handleClick}
            disabled={formLoading}
          />
          </Stack>
      </Box>
    </Box>
  );
};

export default RequirementForm;