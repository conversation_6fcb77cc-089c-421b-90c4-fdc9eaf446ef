import React, { useState } from 'react';
import {
  Button,
  IconButton,
  Menu,
  MenuItem,
  Tooltip,
  <PERSON>nackbar,
  Alert,
  useTheme,
  alpha,
  Box,
  Typography,
  useMediaQuery,
  Divider,
  ListItemIcon,
  ListItemText,
  Fade,
} from '@mui/material';
import {
  Share as ShareIcon,
  WhatsApp as WhatsAppIcon,
  Facebook as FacebookIcon,
  Twitter as TwitterIcon,
  LinkedIn as LinkedInIcon,
  ContentCopy as CopyIcon,
  Email as EmailIcon,
} from '@mui/icons-material';
import axios from 'axios';

interface ShareButtonProps {
  propertyId: string;
  propertyTitle: string;
  propertyImage?: string; // Used for tracking and metadata
  propertyLocation: string;
  propertyPrice: string | undefined;
  shareUrl: string;
  variant?: 'icon' | 'button';
  color?: 'primary' | 'secondary' | 'inherit' | 'success' | 'error' | 'info' | 'warning';
  size?: 'small' | 'medium' | 'large';
  apiUrl: string;
}

const ShareButton: React.FC<ShareButtonProps> = ({
  propertyId,
  propertyTitle,
  propertyImage,
  propertyLocation,
  propertyPrice,
  shareUrl,
  variant = 'icon',
  color = 'primary',
  size = 'medium',
  apiUrl,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };



  // Track share event
  const trackShare = async (platform: string) => {
    try {
      await axios.post(`${apiUrl}/api/v1/exclusive-properties/${propertyId}/track-share`, {
        platform,
      });
    } catch (error) {
      console.error('Error tracking share:', error);
    }
  };

  // Share handlers
  const handleWhatsAppShare = () => {
    const priceText = propertyPrice ? ` for ${propertyPrice}` : '';
    const whatsappText = encodeURIComponent(`Check out this exclusive property: ${propertyTitle} in ${propertyLocation}${priceText}\n\n${shareUrl}`);
    window.open(`https://wa.me/?text=${whatsappText}`, '_blank');
    trackShare('whatsapp');
    handleClose();
  };

  const handleFacebookShare = () => {
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`, '_blank');
    trackShare('facebook');
    handleClose();
  };

  const handleTwitterShare = () => {
    const priceText = propertyPrice ? ` for ${propertyPrice}` : '';
    const twitterText = encodeURIComponent(`Check out this exclusive property: ${propertyTitle} in ${propertyLocation}${priceText}`);
    window.open(`https://twitter.com/intent/tweet?text=${twitterText}&url=${encodeURIComponent(shareUrl)}`, '_blank');
    trackShare('twitter');
    handleClose();
  };

  const handleLinkedInShare = () => {
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`, '_blank');
    trackShare('linkedin');
    handleClose();
  };

  const handleEmailShare = () => {
    const subject = encodeURIComponent(`Exclusive Property: ${propertyTitle}`);
    const priceText = propertyPrice ? ` for ${propertyPrice}` : '';
    const body = encodeURIComponent(`Check out this exclusive property: ${propertyTitle} in ${propertyLocation}${priceText}\n\n${shareUrl}`);
    window.location.href = `mailto:?subject=${subject}&body=${body}`;
    trackShare('email');
    handleClose();
  };

  const handleCopyLink = () => {
    navigator.clipboard.writeText(shareUrl);
    setSnackbarMessage('Link copied to clipboard!');
    setSnackbarOpen(true);
    trackShare('copy');
    handleClose();
  };

  return (
    <>
      {variant === 'icon' ? (
        <Tooltip title="Share this property">
          <IconButton
            onClick={handleClick}
            color={color}
            size={size}
            aria-label="share"
            sx={{
              transition: 'transform 0.2s ease',
              '&:hover': {
                transform: 'scale(1.1)',
                color: theme.palette.primary.main,
              },
            }}
          >
            <ShareIcon />
          </IconButton>
        </Tooltip>
      ) : (
        <Button
          variant="contained"
          color={color}
          startIcon={<ShareIcon />}
          onClick={handleClick}
          size={size}
          sx={{
            borderRadius: 30,
            px: { xs: 2, sm: 3 },
            py: { xs: 0.8, sm: 1 },
            textTransform: 'none',
            fontWeight: 500,
            boxShadow: `0 4px 10px ${alpha(theme.palette.primary.main, 0.25)}`,
            '&:hover': {
              boxShadow: `0 6px 15px ${alpha(theme.palette.primary.main, 0.35)}`,
              transform: 'translateY(-2px)',
            },
            transition: 'all 0.3s ease',
          }}
        >
          Share
        </Button>
      )}

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
        slotProps={{
          paper: {
            elevation: 3,
            sx: {
              borderRadius: 2,
              minWidth: 200,
              overflow: 'visible',
              mt: 1.5,
              '&:before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                left: '50%',
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translate(-50%, -50%) rotate(45deg)',
                zIndex: 0,
              },
              '& .MuiMenuItem-root': {
                py: 1.5,
                px: 2,
                '&:hover': {
                  bgcolor: alpha(theme.palette.primary.main, 0.08),
                },
              },
            }
          }
        }}
        TransitionComponent={Fade}
        transitionDuration={250}
      >
        <Box sx={{ px: 2, py: 1 }}>
          <Typography variant="subtitle2" color="text.secondary" sx={{ fontWeight: 500 }}>
            Share via
          </Typography>
        </Box>
        <Divider sx={{ my: 0.5 }} />

        <MenuItem onClick={handleWhatsAppShare}>
          <ListItemIcon>
            <WhatsAppIcon fontSize="small" sx={{ color: '#25D366' }} />
          </ListItemIcon>
          <ListItemText primary="WhatsApp" />
        </MenuItem>

        <MenuItem onClick={handleFacebookShare}>
          <ListItemIcon>
            <FacebookIcon fontSize="small" sx={{ color: '#1877F2' }} />
          </ListItemIcon>
          <ListItemText primary="Facebook" />
        </MenuItem>

        <MenuItem onClick={handleTwitterShare}>
          <ListItemIcon>
            <TwitterIcon fontSize="small" sx={{ color: '#1DA1F2' }} />
          </ListItemIcon>
          <ListItemText primary="Twitter" />
        </MenuItem>

        <MenuItem onClick={handleLinkedInShare}>
          <ListItemIcon>
            <LinkedInIcon fontSize="small" sx={{ color: '#0A66C2' }} />
          </ListItemIcon>
          <ListItemText primary="LinkedIn" />
        </MenuItem>

        <MenuItem onClick={handleEmailShare}>
          <ListItemIcon>
            <EmailIcon fontSize="small" sx={{ color: '#EA4335' }} />
          </ListItemIcon>
          <ListItemText primary="Email" />
        </MenuItem>

        <Divider sx={{ my: 0.5 }} />

        <MenuItem onClick={handleCopyLink}>
          <ListItemIcon>
            <CopyIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText primary="Copy Link" />
        </MenuItem>
      </Menu>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={handleSnackbarClose}
        anchorOrigin={{
          vertical: isMobile ? 'bottom' : 'bottom',
          horizontal: isMobile ? 'center' : 'right'
        }}
        sx={{
          '& .MuiAlert-root': {
            borderRadius: 2,
            boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
          }
        }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity="success"
          variant="filled"
          sx={{
            alignItems: 'center',
            '& .MuiAlert-icon': {
              fontSize: '1.2rem',
            },
          }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </>
  );
};

export default ShareButton;
