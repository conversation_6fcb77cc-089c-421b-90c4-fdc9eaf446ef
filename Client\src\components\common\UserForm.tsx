import React, { useState, useEffect } from 'react';
import {
    <PERSON><PERSON><PERSON>,
    Grid,
    Ty<PERSON>graphy,
    Button,
    Snackbar,
    FormControl,
    FormHelperText,
    FormGroup,
    FormControlLabel,
    Checkbox,
} from '@mui/material';

interface UserFormProps {
    name?: string;
    email?: string;
    phone?: string;
    workLocation?: string;
    onFormSubmit: (success: boolean) => void;
}

const UserForm: React.FC<UserFormProps> = ({
    name: initialName = '',
    email: initialEmail = '',
    phone: initialPhone = '',
    workLocation: intialLocation = '',
    onFormSubmit
}) => {

    const [name, setName] = useState<string>(initialName);
    const [email, setEmail] = useState<string>(initialEmail);
    const [phoneNumber, setPhoneNumber] = useState<string>(initialPhone);
    const [workLocation, setWorkLocation] = useState<string>(intialLocation);
    const [reraNumber, setReraNumber] = useState<string>('');
    const [isButtonDisabled, setIsButtonDisabled] = useState(false);
    const [formLoading, setFormLoading] = useState(false);
    const [formErrors, setFormErrors] = useState({
        name: '',
        email: '',
        phoneNumber: '',
        workLocation: '',
        reraNumber: '',
        services: '',
    });
    const [formSubmitted, setFormSubmitted] = useState(false);
    const [apiUrl, setApiUrl] = useState(import.meta.env.VITE_API_URL);
    const [open, setOpen] = useState(false);
    const [showThankYou, setShowThankYou] = useState(false);
    const [services, setServices] = useState({
        buySell: false,
        rent: false,
        commercial: false,
        residential: false,
    });

    useEffect(() => {
        setName(initialName);
        setEmail(initialEmail);
        setPhoneNumber(initialPhone);
    }, [initialName, initialEmail, initialPhone]);

    const handleServiceChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setServices({
            ...services,
            [event.target.name]: event.target.checked,
        });
    };

    const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
        event.preventDefault();
        setIsButtonDisabled(true);
        setFormLoading(true);
        setFormErrors({ // Resetting errors on submit
            name: '',
            email: '',
            phoneNumber: '',
            workLocation: '',
            reraNumber: '',
            services: '',
        });

        // Clean and format the data
        const cleanedName = name.trim();
        const cleanedEmail = email.trim().toLowerCase();
        const cleanedPhoneNumber = phoneNumber.trim();
        const cleanedWorkLocation = workLocation.trim().toLowerCase();
        const cleanedReraNumber = reraNumber.trim();

        // Create a request payload object directly instead of using FormData
        const requestData = {
            name: cleanedName,
            email: cleanedEmail,
            phoneNumber: cleanedPhoneNumber,
            workLocation: cleanedWorkLocation || '',
            reraNumber: cleanedReraNumber || 'None',
            services // Send the services object directly
        };
        console.log('Services being sent:', services);

        const selectedServices = Object.values(services).filter(Boolean).length;

        const errors = {
            name: cleanedName === '' ? 'Name is required' : '',
            email: !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(cleanedEmail) ? 'Invalid email address' : (cleanedEmail === '' ? 'Email is required' : ''),
            phoneNumber: cleanedPhoneNumber === '' || cleanedPhoneNumber.length !== 10 ? 'Phone number is required and must be 10 digits' : '',
            workLocation: cleanedWorkLocation === '' ? 'Work location is required' : '',
            reraNumber: cleanedReraNumber !== '' && cleanedReraNumber.length > 15 ? 'RERA number must not exceed 15 characters if provided' : '',
            services: selectedServices === 0 ? 'Please select at least one service' : '',
        };

        if (Object.values(errors).some(error => error !== '')) {
            setFormErrors(errors);
            setIsButtonDisabled(false);
            setFormLoading(false);
            return;
        }

        try {
            const response = await fetch(`${apiUrl}/api/v1/users/update`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData),
            });

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            const data = await response.json();

            setFormLoading(false);
            setFormSubmitted(true);
            setShowThankYou(true);
            setOpen(true);
            onFormSubmit(true);

            setTimeout(() => {
                window.location.reload();
            }, 2000);

        } catch (error) {
            console.error('There was a problem with the fetch operation:', error);
            setFormLoading(false);
            onFormSubmit(false);
        } finally {
            setIsButtonDisabled(false);
        }
    };

    const handleButtonClick = (e: React.MouseEvent<HTMLButtonElement>) => {
        e.preventDefault();
        handleSubmit(e as any);
    };

    const handleCloseSnackbar = () => {
        setOpen(false);
    };


    return (
        <form onSubmit={handleSubmit} style={{ width: '100%', maxWidth: '600px', padding: "20px" }}>
            <Typography variant="h6" component="h2" gutterBottom sx={{ textAlign: 'center' }}>Agent Form</Typography>
            <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                    <FormControl fullWidth error={!!formErrors.name}>
                        <TextField
                            required
                            fullWidth
                            label="Name"
                            variant="outlined"
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                            error={!!formErrors.name}
                        />
                        <FormHelperText>{formErrors.name}</FormHelperText>
                    </FormControl>
                </Grid>
                <Grid item xs={12} md={6}>
                    <FormControl fullWidth error={!!formErrors.phoneNumber}>
                        <TextField
                            required
                            fullWidth
                            label="Phone Number"
                            variant="outlined"
                            value={phoneNumber}
                            onChange={(e) => setPhoneNumber(e.target.value)}
                            error={!!formErrors.phoneNumber}
                        />
                        <FormHelperText>{formErrors.phoneNumber}</FormHelperText>
                    </FormControl>
                </Grid>
                <Grid item xs={12} md={6}>
                    <FormControl fullWidth error={!!formErrors.email}>
                        <TextField
                            required
                            fullWidth
                            label="Email"
                            variant="outlined"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            error={!!formErrors.email}
                        />
                        <FormHelperText>{formErrors.email}</FormHelperText>
                    </FormControl>
                </Grid>
                <Grid item xs={12} md={6}>
                    <FormControl fullWidth error={!!formErrors.workLocation}>
                        <TextField
                            required
                            label="Work Location (e.g - Vijay Nagar, Indore)"
                            variant="outlined"
                            error={!!formErrors.workLocation}
                            value={workLocation || ''}
                            onChange={(e) => setWorkLocation(e.target.value)}
                            fullWidth
                        />
                        <FormHelperText>{formErrors.workLocation}</FormHelperText>
                    </FormControl>
                </Grid>
                <Grid item xs={12} md={6} sx={{ margin: '10px 0' }}>
                    <FormControl fullWidth error={!!formErrors.reraNumber}>
                        <TextField
                            fullWidth
                            label="RERA Number (Optional)"
                            variant="outlined"
                            value={reraNumber}
                            onChange={(e) => setReraNumber(e.target.value)}
                            error={!!formErrors.reraNumber}
                            inputProps={{ maxLength: 15 }} // Adding maxLength attribute
                        />
                        <FormHelperText>{formErrors.reraNumber ? formErrors.reraNumber : "If you do not have a RERA number, you can leave this field empty."}</FormHelperText>
                    </FormControl>
                </Grid>
                <Grid item xs={12}>
                    <FormControl component="fieldset" error={!!formErrors.services} fullWidth>
                        <Typography variant="subtitle1" gutterBottom>Specialized in</Typography>
                        <FormGroup row>
                            <FormControlLabel
                                control={
                                    <Checkbox 
                                        checked={services.buySell}
                                        onChange={handleServiceChange}
                                        name="buySell"
                                    />
                                }
                                label="Buy/Sell"
                            />
                            <FormControlLabel
                                control={
                                    <Checkbox 
                                        checked={services.rent}
                                        onChange={handleServiceChange}
                                        name="rent"
                                    />
                                }
                                label="Rent"
                            />
                            <FormControlLabel
                                control={
                                    <Checkbox 
                                        checked={services.commercial}
                                        onChange={handleServiceChange}
                                        name="commercial"
                                    />
                                }
                                label="Commercial"
                            />
                            <FormControlLabel
                                control={
                                    <Checkbox 
                                        checked={services.residential}
                                        onChange={handleServiceChange}
                                        name="residential"
                                    />
                                }
                                label="Residential"
                            />
                        </FormGroup>
                        <FormHelperText>{formErrors.services}</FormHelperText>
                    </FormControl>
                </Grid>
            </Grid>
            <Button
                onClick={handleButtonClick}
                variant="contained"
                style={{ backgroundColor: "#0F52BA", borderRadius: "20px", marginTop: '20px' }}
                disabled={formLoading || isButtonDisabled}
            >
                {formLoading ? "Submitting..." : "Submit"}
            </Button>
            <Snackbar
                open={open}
                autoHideDuration={2000}
                onClose={handleCloseSnackbar}
                message={showThankYou ? "Thank you for your submission! Page will refresh..." : "Thank you for your submission!"}
            />
        </form>
    );
};

export default UserForm;