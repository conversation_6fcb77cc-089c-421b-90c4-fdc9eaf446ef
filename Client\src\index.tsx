import React from "react";
import { createRoot } from "react-dom/client";
import App from "./App";
import brickbix from './assets/brick-bix.png'; // Ensure the correct path to your logo

// Set the page title
document.title = "BrickBix - Your Property Solution";

// Function to set the favicon
const setFavicon = (url: string) => {
  let link = document.querySelector("link[rel*='icon']") as HTMLLinkElement || document.createElement('link');
  link.type = 'image/png';
  link.rel = 'shortcut icon';
  link.href = url;
  const head = document.getElementsByTagName('head')[0];
  if (head) {
    head.appendChild(link);
  }
};

// Set the favicon to the BrickBix logo
setFavicon(brickbix);

// Function to dynamically set meta tags
const setMetaTags = () => {
  const metaTags = [
    { name: "description", content: "BrickBix is your ultimate property solution. Explore, manage, and collaborate on properties seamlessly." },
    { property: "og:title", content: "BrickBix - Your Property Solution" },
    { property: "og:description", content: "Discover and share properties effortlessly with BrickBix." },
    { property: "og:image", content: brickbix }, // Use the logo for social sharing
    { property: "og:url", content: window.location.href },
    { name: "twitter:card", content: "summary_large_image" },
    { name: "twitter:title", content: "BrickBix - Your Property Solution" },
    { name: "twitter:description", content: "Find and manage properties easily with BrickBix." },
    { name: "twitter:image", content: brickbix }, // Logo for Twitter cards
  ];

  const head = document.getElementsByTagName("head")[0];

  metaTags.forEach(tag => {
    const meta = document.createElement("meta");
    Object.entries(tag).forEach(([key, value]) => {
      meta.setAttribute(key, value);
    });
    if (head) {
      head.appendChild(meta);
    }
  });
};

// Set the meta tags dynamically
setMetaTags();

const container = document.getElementById("root") as HTMLElement;
const root = createRoot(container);

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
