import { <PERSON><PERSON>ey } from "@refinedev/core";
import { ReactNode } from "react";

export interface AgentCardProp {
  id?: BaseKey | undefined;
  name: string;
  email: string;
  avatar: string;
  noOfProperties: number;
  workLocation: string;
  services?: {
    buySell: boolean;
    rent: boolean;
    commercial: boolean;
    residential: boolean;
  };
}

export interface InfoBarProps {
  icon: ReactNode;
  name: string;
}
