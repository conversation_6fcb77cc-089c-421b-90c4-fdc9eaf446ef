import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { CircularProgress, Box, Typography, Container, Paper, Button } from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { useNavigate } from "react-router-dom";
import Profile from "../components/common/Profile";
import { useGetIdentity, useOne } from "@refinedev/core";
import { useActiveAuthProvider } from "@refinedev/core";

const AgentProfile = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [error, setError] = useState<string | null>(null);
  const apiUrl = import.meta.env.VITE_API_URL;
  
  // Get current user for comparison
  const authProvider = useActiveAuthProvider();
  const { data: currentUser } = useGetIdentity({
    v3LegacyAuthProviderCompatible: Boolean(authProvider?.isLegacy),
  });

  // Fetch agent data
  const { data, isLoading, isError } = useOne({
    resource: "users",
    id: id as string,
  });

  const agentData = data?.data ?? {};

  // Log for debugging
  useEffect(() => {
    console.log("Agent profile component - ID:", id);
    console.log("Agent data:", agentData);
    console.log("Loading state:", isLoading);
    console.log("Error state:", isError);
  }, [id, agentData, isLoading, isError]);

  const handleBackClick = () => {
    navigate('/agent');
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="80vh">
        <CircularProgress />
      </Box>
    );
  }

  if (isError || !agentData || typeof agentData !== "object" || !("name" in agentData)) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Paper 
          elevation={3} 
          sx={{ 
            p: 4, 
            textAlign: 'center',
            borderRadius: '12px',
            borderLeft: '4px solid #d84030'
          }}
        >
          <Typography variant="h5" color="error" gutterBottom>
            {error || "Agent not found"}
          </Typography>
          <Button 
            startIcon={<ArrowBackIcon />} 
            variant="contained" 
            onClick={handleBackClick}
            sx={{ mt: 2 }}
          >
            Back to Agents
          </Button>
        </Paper>
      </Container>
    );
  }

  // Check if viewing own profile
  const isOwnProfile = currentUser?.email === agentData.email;

  // If it's the user's own profile, redirect to my-profile
  if (isOwnProfile) {
    navigate('/my-profile');
    return null;
  }

  return (
    <Profile
      type="Agent"
      name={agentData.name}
      email={agentData.email}
      phone={agentData.phoneNumber || ''}
      workLocation={agentData.workLocation || ''}
      avatar={agentData.avatar}
      properties={agentData.allProperties || []}
      requirement={agentData.allRequirements || []}
      currentUserId={currentUser?.userid}
      profileUserId={agentData._id}
      services={agentData.services}
      reraNumber={agentData.reraNumber}
      isOwnProfile={false}
    />
  );
};

export default AgentProfile; 