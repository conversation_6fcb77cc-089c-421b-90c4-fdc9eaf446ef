import { useList, BaseRecord } from "@refinedev/core";
import { useEffect, useState, useMemo } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import {
    CircularProgress,
    Fab,
    Zoom,
    TextField,
    InputAdornment,
    Pagination,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    SelectChangeEvent,
    Container,
    Paper,
    Grid,
    useMediaQuery,
    useTheme,
    Chip
} from "@mui/material";
import AgentCard from "../components/agent/AgentCard";
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import SearchIcon from '@mui/icons-material/Search';
import PeopleAltIcon from '@mui/icons-material/PeopleAlt';
import FilterListIcon from '@mui/icons-material/FilterList';

interface AgentType {
    _id: string;
    name: string;
    email: string;
    avatar: string;
    workLocation?: string;
    allProperties?: any[];
    services?: {
        buySell: boolean;
        rent: boolean;
        commercial: boolean;
        residential: boolean;
    };
}

const Agents = () => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const isTablet = useMediaQuery(theme.breakpoints.down('md'));

    const { data, isLoading, isError } = useList({
        resource: "users",
    });
    // Cached agents for fallback when API fails
    const [, setCachedAgents] = useState<AgentType[]>([]);
    const allAgents = data?.data ?? [] as AgentType[];
    const [showScroll, setShowScroll] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const [searchType, setSearchType] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const agentsPerPage = 50;

    useEffect(() => {
        if (allAgents.length > 0) {
            localStorage.setItem("agents", JSON.stringify(allAgents));
        }
    }, [allAgents]);

    useEffect(() => {
        const cachedData = localStorage.getItem("agents");
        if (cachedData) {
            setCachedAgents(JSON.parse(cachedData));
        }
    }, [isError]);

    useEffect(() => {
        const handleScroll = () => {
            if (window.scrollY > 300) {
                setShowScroll(true);
            } else {
                setShowScroll(false);
            }
        };

        window.addEventListener('scroll', handleScroll);
        return () => {
            window.removeEventListener('scroll', handleScroll);
        };
    }, []);

    const scrollToTop = () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    };

    // Filter agents based on search query
    const filteredAgents = useMemo(() => {
        const agents = [...allAgents];

        if (!searchQuery) return agents;

        return agents.filter(agent => {
            const nameMatch = agent.name && agent.name.toLowerCase().includes(searchQuery.toLowerCase());
            const locationMatch = agent.workLocation && agent.workLocation.toLowerCase().includes(searchQuery.toLowerCase());

            if (searchType === 'name') return nameMatch;
            if (searchType === 'location') return locationMatch;
            return nameMatch || locationMatch; // 'all' option
        });
    }, [allAgents, searchQuery, searchType]);

    // Shuffle the filtered agents to display them in random order
    const shuffledAgents = useMemo(() => {
        const agents = [...filteredAgents];
        // Fisher-Yates shuffle algorithm
        for (let i = agents.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            // Skip the swap if either agent is undefined
            if (agents[i] !== undefined && agents[j] !== undefined) {
                // Use type assertion to tell TypeScript these are valid agents
                const agentI = agents[i] as (BaseRecord & AgentType);
                const agentJ = agents[j] as (BaseRecord & AgentType);
                agents[i] = agentJ;
                agents[j] = agentI;
            }
        }
        return agents;
    }, [filteredAgents]);

    // Get current page of agents with pagination
    const indexOfLastAgent = currentPage * agentsPerPage;
    const indexOfFirstAgent = indexOfLastAgent - agentsPerPage;
    const currentAgents = shuffledAgents.slice(indexOfFirstAgent, indexOfLastAgent);
    const totalPages = Math.ceil(filteredAgents.length / agentsPerPage);

    // Handle page change
    const handlePageChange = (_: unknown, value: number) => {
        setCurrentPage(value);
        scrollToTop();
    };

    // Handle search input change
    const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setSearchQuery(event.target.value);
        setCurrentPage(1); // Reset to first page on new search
    };

    // Handle search type change
    const handleSearchTypeChange = (event: SelectChangeEvent) => {
        setSearchType(event.target.value);
        setCurrentPage(1); // Reset to first page on search type change
    };

    if (isLoading) return (
        <Box sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100vh'
        }}>
            <CircularProgress color="primary" />
        </Box>
    );

    return (
        <Container maxWidth="lg" sx={{ pb: 8 }}>
            <Paper
                elevation={0}
                sx={{
                    borderRadius: '16px',
                    overflow: 'hidden',
                    background: 'linear-gradient(135deg, #f5f7fa 0%, #eef2f7 100%)',
                    p: { xs: 2, sm: 4 },
                    mb: 4
                }}
            >
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        mb: 3,
                        position: 'relative'
                    }}
                >
                    <PeopleAltIcon sx={{
                        fontSize: 40,
                        color: '#11418a',
                        mb: 1
                    }} />

                    <Typography
                        fontSize={{ xs: 28, sm: 36 }}
                        fontWeight={700}
                        color="#11142d"
                        textAlign="center"
                        mb={1}
                        sx={{
                            textShadow: '1px 1px 2px rgba(0,0,0,0.1)',
                            position: 'relative',
                        }}
                        data-testid="header-user-name"
                    >
                        <span style={{ color: "#d84030" }}>Agent</span>{" "}
                        <span style={{ color: "#11418a" }}>Directory</span>
                    </Typography>

                    <Typography
                        variant="subtitle1"
                        color="text.secondary"
                        align="center"
                        sx={{ maxWidth: '600px', mx: 'auto' }}
                    >
                        Connect with our network of real estate professionals to help you find your perfect property
                    </Typography>
                </Box>

                {/* Search Section */}
                <Paper
                    elevation={2}
                    sx={{
                        p: { xs: 2, sm: 3 },
                        borderRadius: '12px',
                        backgroundColor: 'white',
                        mb: 3
                    }}
                >
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <FilterListIcon sx={{ mr: 1, color: '#11418a' }} />
                        <Typography variant="h6" fontWeight={600} color="#11142d">
                            Find Agents
                        </Typography>
                    </Box>

                    <Box sx={{
                        display: 'flex',
                        flexDirection: { xs: 'column', sm: 'row' },
                        gap: 2
                    }}>
                        <TextField
                            fullWidth
                            variant="outlined"
                            placeholder="Search agents..."
                            value={searchQuery}
                            onChange={handleSearchChange}
                            InputProps={{
                                startAdornment: (
                                    <InputAdornment position="start">
                                        <SearchIcon color="action" />
                                    </InputAdornment>
                                ),
                                sx: { borderRadius: '8px' }
                            }}
                            size="medium"
                            sx={{ flexGrow: 1 }}
                        />
                        <FormControl sx={{ minWidth: { xs: '100%', sm: 180 } }}>
                            <InputLabel id="search-type-label">Search By</InputLabel>
                            <Select
                                labelId="search-type-label"
                                value={searchType}
                                onChange={handleSearchTypeChange}
                                label="Search By"
                                size="medium"
                            >
                                <MenuItem value="all">All Fields</MenuItem>
                                <MenuItem value="name">Name</MenuItem>
                                <MenuItem value="location">Location</MenuItem>
                            </Select>
                        </FormControl>
                    </Box>

                    {/* Results count */}
                    {searchQuery && (
                        <Box sx={{ mt: 2 }}>
                            <Chip
                                label={`${filteredAgents.length} results found`}
                                color="primary"
                                size="small"
                                variant="outlined"
                            />
                        </Box>
                    )}
                </Paper>

                {/* Results info */}
                <Box sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    mb: 2,
                    alignItems: 'center',
                    px: 1
                }}>
                    <Typography variant="body2" color="text.secondary">
                        Showing {currentAgents.length} of {filteredAgents.length} agents
                    </Typography>

                    {filteredAgents.length > agentsPerPage && (
                        <Typography variant="body2" color="text.secondary">
                            Page {currentPage} of {totalPages}
                        </Typography>
                    )}
                </Box>

                {/* Agent Cards Grid */}
                <Grid
                    container
                    spacing={3}
                    sx={{
                        mt: 1,
                        '& > *': {
                            transition: 'transform 0.3s ease-in-out',
                            '&:hover': {
                                transform: 'translateY(-5px)'
                            }
                        }
                    }}
                >
                    {currentAgents.map((agent) => (
                        <Grid item xs={12} sm={6} md={4} key={agent._id}>
                            <AgentCard
                                id={agent._id}
                                name={agent.name}
                                email={agent.email}
                                avatar={agent.avatar}
                                noOfProperties={agent.allProperties?.length || 0}
                                workLocation={agent.workLocation || "Indore, Madhya Pradesh"}
                                services={agent.services}
                            />
                        </Grid>
                    ))}
                </Grid>

                {currentAgents.length === 0 && searchQuery && (
                    <Paper
                        elevation={0}
                        sx={{
                            p: 4,
                            textAlign: 'center',
                            borderRadius: '12px',
                            backgroundColor: '#f8f9fa',
                            borderLeft: '4px solid #d84030'
                        }}
                    >
                        <Typography variant="h6" color="text.secondary" gutterBottom>
                            No agents found
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                            Try adjusting your search or search criteria
                        </Typography>
                    </Paper>
                )}

                {/* Pagination */}
                {filteredAgents.length > agentsPerPage && (
                    <Box sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        mt: 4,
                        mb: 2
                    }}>
                        <Pagination
                            count={totalPages}
                            page={currentPage}
                            onChange={handlePageChange}
                            color="primary"
                            showFirstButton
                            showLastButton
                            size={isMobile ? "small" : "medium"}
                            siblingCount={isTablet ? 0 : 1}
                        />
                    </Box>
                )}

                <Zoom in={showScroll}>
                    <Box
                        role="presentation"
                        sx={{ position: 'fixed', bottom: 24, right: 24, zIndex: 1000 }}
                    >
                        <Fab
                            color="primary"
                            aria-label="scroll back to top"
                            onClick={scrollToTop}
                            sx={{
                                boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                                '&:hover': {
                                    boxShadow: '0 6px 16px rgba(0,0,0,0.2)'
                                }
                            }}
                        >
                            <KeyboardArrowUpIcon />
                        </Fab>
                    </Box>
                </Zoom>
            </Paper>
        </Container>
    );
};

export default Agents;