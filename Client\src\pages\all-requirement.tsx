import { useMemo, useState, useEffect } from "react";
import { Add, Sort, FilterList, Search, ArrowBack, ArrowForward } from "@mui/icons-material";
import { useTable } from "@refinedev/core";
import {
  Box,
  Typography,
  MenuItem,
  Skeleton,
  TextField,
  Grid,
  Button,
  Paper,
  Fade,
  InputAdornment,
  Container,
  Chip,
  Divider,
  useTheme,
  alpha,
  IconButton,
  Menu,
} from "@mui/material";
import { useNavigate } from "react-router-dom";
import PropertyCard from "../components/common/PropertyCard";
import BrickBixImage from '../assets/brick bix image.jpg';

const Requirement = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  const {
    tableQueryResult: { data, isLoading, isError },
    current,
    setCurrent,
    pageSize,
    pageCount,
    setFilters,
    setSorters,
    sorters,
  } = useTable({
    resource: "requirement",
    initialSorter: [{ field: "createdAt", order: "desc" }],
    pagination: {
      pageSize: 30,
    },
  });

  const requirementValues = data?.data ?? [];

  const [filteredPageCount, setFilteredPageCount] = useState<number>(pageCount);
  const [searchValue, setSearchValue] = useState<string>("");
  const [activeFilters, setActiveFilters] = useState<{propertyType?: string, price?: string}>({});
  const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null);
  const [propertyTypeFilter, setPropertyTypeFilter] = useState<string>("");

  const currentPrice = sorters.find((item) => item.field === "askedPrice")?.order;

  const handleFilterClick = (event: React.MouseEvent<HTMLElement>) => {
    setFilterAnchorEl(event.currentTarget);
  };

  const handleFilterClose = () => {
    setFilterAnchorEl(null);
  };

  const toggleSort = (field: string) => {
    const newOrder = currentPrice === "asc" ? "desc" : "asc";
    setSorters([{ field, order: newOrder }]);
    setActiveFilters(prev => ({...prev, price: newOrder}));
    handleFilterClose();
  };

  const debouncedSearch = useMemo(() => {
    let timeout: number;
    return (value: string) => {
      setSearchValue(value);
      clearTimeout(timeout);
      timeout = window.setTimeout(() => {
        setFilters([
          { field: "title", operator: "contains", value: value || undefined },
          { field: "location", operator: "contains", value: value || undefined },
        ]);
        setCurrent(1);
      }, 500);
    };
  }, [setFilters, setCurrent]);

  const handlePropertyTypeFilter = (value: string) => {
    setPropertyTypeFilter(value);
    setFilters(
      [{ field: "propertyType", operator: "eq", value: value || undefined }],
      "replace"
    );
    setCurrent(1);
    setActiveFilters(prev => ({...prev, propertyType: value || undefined}));
    handleFilterClose();
  };

  const clearFilters = () => {
    setFilters([], "replace");
    setSearchValue("");
    setPropertyTypeFilter("");
    setActiveFilters({});
    setSorters([{ field: "createdAt", order: "desc" }]);
  };

  useEffect(() => {
    if (data) {
      const totalProperties = data.total;
      const updatedPageCount = Math.ceil(totalProperties / pageSize);
      setFilteredPageCount(updatedPageCount);

      if (requirementValues.length === 0 || current > updatedPageCount) {
        setCurrent(1);
      }
    }
  }, [data, pageSize, requirementValues.length, current]);

  const checkURLValue = (url: string): string => {
    const urlSegments = url.split("/");
    const lastSegment = urlSegments[urlSegments.length - 1] || "";
    return lastSegment.includes("requirement") ? "properties-requirement" : "properties";
  };

  const fullUrl = window.location.href;
  const fullUrlValue = checkURLValue(fullUrl);

  if (isLoading) {
    return (
      <Container maxWidth="lg" sx={{ py: { xs: 2, sm: 3, md: 4 } }}>
        <Fade in timeout={800}>
          <Paper
            elevation={0}
            sx={{
              p: { xs: 2, sm: 3 },
              borderRadius: 2,
              background: alpha(theme.palette.background.paper, 0.8),
              mb: 3
            }}
          >
            <Skeleton variant="text" width={300} height={40} sx={{ borderRadius: '20px' }} />
            <Skeleton variant="text" width="60%" height={30} sx={{ mb: 2, borderRadius: '20px' }} />
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
              <Skeleton variant="rectangular" width="70%" height={50} sx={{ borderRadius: '12px' }} />
              <Skeleton variant="rectangular" width="25%" height={50} sx={{ borderRadius: '12px' }} />
      </Box>
          </Paper>
        </Fade>

        <Grid container spacing={3}>
          {Array.from(new Array(6)).map((_, index) => (
            <Grid item key={index} xs={12} sm={6} md={4} lg={4}>
              <Skeleton
                variant="rectangular"
                width="100%"
                height={300}
                sx={{
                  borderRadius: '20px',
                  transform: 'scale(0.98)',
                  transition: 'transform 0.3s ease'
                }}
              />
            </Grid>
          ))}
        </Grid>
      </Container>
    );
  }

  if (isError) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Paper
          elevation={0}
          sx={{
            p: 3,
            borderRadius: 2,
            textAlign: 'center',
            backgroundColor: '#fff0f0',
          }}
        >
          <Typography variant="h5" color="error" gutterBottom>
            Error fetching requirements
          </Typography>
          <Typography variant="body1" mb={3}>
            We couldn't load the requirements data. Please try again later.
          </Typography>
          <Button
            variant="contained"
            color="primary"
            onClick={() => window.location.reload()}
            sx={{ borderRadius: '20px' }}
          >
            Retry
          </Button>
        </Paper>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: { xs: 2, sm: 3, md: 4 } }}>
      <Fade in timeout={800}>
        <Paper
          elevation={0}
          sx={{
            p: { xs: 2, sm: 3 },
            borderRadius: 2,
            background: alpha(theme.palette.background.paper, 0.8),
            mb: 3
          }}
        >
          <Typography
            variant="h4"
            sx={{
              fontWeight: 700,
              fontSize: { xs: '1.5rem', sm: '1.75rem', md: '2rem' },
              mb: 1
            }}
          >
            <span style={{ color: '#d84030' }}>{!requirementValues.length ? "No" : "All"}</span>{' '}
            <span style={{ color: '#11418a' }}>Requirements</span>
            {!requirementValues.length && <span style={{ color: '#d84030' }}> found</span>}
          </Typography>

        <Box
          display="flex"
          flexDirection={{ xs: "column", sm: "row" }}
          justifyContent="space-between"
          alignItems="center"
            gap={2}
            mt={2}
        >
            <Box sx={{ position: 'relative', width: { xs: "100%", sm: "70%" } }}>
          <TextField
            variant="outlined"
                color="primary"
                placeholder="Search by title, location, or property type"
            fullWidth
                value={searchValue}
                onChange={(e) => debouncedSearch(e.currentTarget.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search color="action" />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        size="small"
                        onClick={handleFilterClick}
            sx={{
                          color: Object.keys(activeFilters).length > 0 ? 'primary.main' : 'action.active',
                          bgcolor: Object.keys(activeFilters).length > 0
                            ? alpha(theme.palette.primary.main, 0.1)
                            : alpha(theme.palette.background.paper, 0.8),
                          borderRadius: '50%',
                          p: 0.5,
                          border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
              '&:hover': {
                            bgcolor: alpha(theme.palette.primary.main, 0.1),
                            border: `1px solid ${alpha(theme.palette.primary.main, 0.3)}`
                          }
                        }}
                      >
                        <FilterList fontSize="small" />
                      </IconButton>
                    </InputAdornment>
                  )
                }}
                sx={{
                  "& .MuiInputBase-root": {
                    height: "48px",
                    fontSize: "16px",
                    borderRadius: "12px",
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: alpha(theme.palette.primary.main, 0.2),
                  },
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: alpha(theme.palette.primary.main, 0.5),
                  },
                  boxShadow: `0 3px 5px ${alpha(theme.palette.common.black, 0.05)}`
                }}
              />
              <Menu
                anchorEl={filterAnchorEl}
                open={Boolean(filterAnchorEl)}
                onClose={handleFilterClose}
                PaperProps={{
                  elevation: 3,
                  sx: {
                    mt: 1,
                    borderRadius: 2,
                    minWidth: 200,
                    boxShadow: `0 5px 15px ${alpha(theme.palette.common.black, 0.1)}`
                  }
                }}
                transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
              >
                <Box sx={{ p: 1 }}>
                  <Typography variant="subtitle2" sx={{ px: 1, py: 0.5, fontWeight: 600 }}>
                    Sort by
                  </Typography>
                  <MenuItem
                    onClick={() => toggleSort("askedPrice")}
                    sx={{
                      borderRadius: 1,
                      my: 0.5,
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center'
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Sort fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                      <Typography variant="body2">Price</Typography>
                    </Box>
                    {currentPrice && (
                      <Typography variant="caption" color="primary">
                        {currentPrice === "asc" ? "Low to High" : "High to Low"}
                      </Typography>
                    )}
                  </MenuItem>

                  <Divider sx={{ my: 1 }} />

                  <Typography variant="subtitle2" sx={{ px: 1, py: 0.5, fontWeight: 600 }}>
                    Property Type
                  </Typography>
                  <Box sx={{ maxHeight: 200, overflow: 'auto' }}>
                    <MenuItem
                      onClick={() => handlePropertyTypeFilter("")}
                      sx={{
                        borderRadius: 1,
                        my: 0.5,
                        bgcolor: !propertyTypeFilter ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
                        color: !propertyTypeFilter ? 'primary.main' : 'text.primary',
                        '&:hover': {
                          bgcolor: alpha(theme.palette.primary.main, 0.1)
                        }
                      }}
                    >
                      <Typography variant="body2">All Categories</Typography>
                    </MenuItem>
                {[
                  "Apartment",
                  "Rental",
                  "Farmhouse",
                  "Commercial",
                  "Land",
                  "Duplex",
                  "Plot",
                  "Room",
                ].map((type) => (
                      <MenuItem
                        key={type}
                        onClick={() => handlePropertyTypeFilter(type.toLowerCase())}
                        sx={{
                          borderRadius: 1,
                          my: 0.5,
                          bgcolor: propertyTypeFilter === type.toLowerCase() ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
                          color: propertyTypeFilter === type.toLowerCase() ? 'primary.main' : 'text.primary',
                          '&:hover': {
                            bgcolor: alpha(theme.palette.primary.main, 0.1)
                          }
                        }}
                      >
                        <Typography variant="body2">{type}</Typography>
                  </MenuItem>
                ))}
                  </Box>

                  {(Object.keys(activeFilters).length > 0 || propertyTypeFilter) && (
                    <>
                      <Divider sx={{ my: 1 }} />
                      <MenuItem
                        onClick={clearFilters}
                        sx={{
                          borderRadius: 1,
                          color: 'error.main',
                          '&:hover': {
                            bgcolor: alpha(theme.palette.error.main, 0.1)
                          }
                        }}
                      >
                        <Typography variant="body2">Clear all filters</Typography>
                      </MenuItem>
                    </>
                  )}
                </Box>
              </Menu>
            </Box>
            <Divider
              orientation="vertical"
              flexItem
              sx={{
                display: { xs: 'none', sm: 'block' },
                borderColor: alpha(theme.palette.divider, 0.2),
                height: 'auto',
                mx: 1
              }}
            />
            <Button
              variant="contained"
              color="primary"
              onClick={() => navigate("properties-requirement/create")}
              startIcon={<Add />}
              sx={{
                width: { xs: "100%", sm: "auto" },
                borderRadius: "12px",
                height: "48px",
                textTransform: "none",
                px: 3,
                fontWeight: 600,
                boxShadow: 2,
                '&:hover': {
                  boxShadow: 4,
                  transform: "translateY(-2px)",
                },
                transition: "all 0.3s ease"
              }}
            >
              Add New Requirement
            </Button>
          </Box>

          {(Object.keys(activeFilters).length > 0 || searchValue || propertyTypeFilter) && (
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
              {searchValue && (
                <Chip
                  label={`Search: ${searchValue}`}
                  onDelete={() => debouncedSearch("")}
                  color="primary"
                  size="small"
                  sx={{ borderRadius: '12px' }}
                />
              )}
              {propertyTypeFilter && (
                <Chip
                  label={`Type: ${propertyTypeFilter}`}
                  onDelete={() => handlePropertyTypeFilter("")}
                  color="primary"
                  size="small"
                  sx={{ borderRadius: '12px' }}
                />
              )}
              {activeFilters.price && (
                <Chip
                  label={`Price: ${activeFilters.price === 'asc' ? 'Low to High' : 'High to Low'}`}
                  onDelete={() => {
                    setSorters([{ field: "createdAt", order: "desc" }]);
                    setActiveFilters(prev => ({...prev, price: undefined}));
                  }}
                  color="primary"
                  size="small"
                  sx={{ borderRadius: '12px' }}
                />
              )}
              {(Object.keys(activeFilters).length > 0 || searchValue || propertyTypeFilter) && (
                <Chip
                  label="Clear all filters"
                  onClick={clearFilters}
                  color="secondary"
                  size="small"
                  sx={{ borderRadius: '12px' }}
                />
              )}
            </Box>
          )}
        </Paper>
      </Fade>

      {requirementValues.length > 0 ? (
        <Fade in timeout={1000}>
          <Grid
          container
          spacing={3}
            sx={{
              mt: 1,
              animation: "fadeIn 0.5s ease-in-out",
            }}
          >
            {requirementValues.map((requirement, index) => (
              <Grid
                key={requirement._id}
                item
                xs={12}
                sm={6}
                md={6}
                lg={4}
                sx={{
                  transform: "scale(0.98)",
                  transition: "transform 0.3s ease",
                  "&:hover": {
                    transform: "scale(1)",
                  },
                }}
              >
                <Fade
                  in={true}
                  style={{
                    transitionDelay: `${index * 100}ms`,
                    transformOrigin: "center",
                  }}
                >
                  <Box>
                <PropertyCard
                  id={requirement._id}
                  title={requirement.title}
                  location={requirement.location}
                  dealType={requirement.dealType}
                  price={requirement.askedPrice}
                  photo={BrickBixImage}
                  phone={requirement.phone}
                  propertyType={requirement.propertyType}
                  url={fullUrlValue}
                />
                  </Box>
                </Fade>
              </Grid>
            ))}
          </Grid>
        </Fade>
      ) : (
        <Fade in timeout={800}>
          <Paper
            elevation={0}
            sx={{
              p: 4,
              borderRadius: 2,
              textAlign: 'center',
              backgroundColor: alpha(theme.palette.background.paper, 0.5),
              border: `1px dashed ${alpha(theme.palette.primary.main, 0.3)}`,

            }}
          >
            <Typography variant="h6" color="textSecondary" gutterBottom>
              No requirements match your search criteria
            </Typography>
            <Typography variant="body2" color="textSecondary" paragraph>
              Try adjusting your filters or search term
            </Typography>
            <Button
              variant="outlined"
              color="primary"
              onClick={clearFilters}
              sx={{
                borderRadius: '20px',
                mt: 1,
                textTransform: 'none',
              }}
            >
              Clear all filters
            </Button>
          </Paper>
        </Fade>
      )}

      {requirementValues.length > 0 && filteredPageCount > 1 && (
        <Paper
          elevation={0}
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            gap: 2,

            py: 2,
            borderRadius: 2,
            backgroundColor: alpha(theme.palette.background.paper, 0.5),
          }}
        >
          <Button
            variant="outlined"
            color="primary"
            onClick={() => {
                setCurrent((prev) => Math.max(prev - 1, 1));
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }}
              disabled={current === 1}
            startIcon={<ArrowBack />}
            sx={{
              borderRadius: "10px",
              textTransform: "none",
              fontWeight: 600,
              borderWidth: current === 1 ? "1px" : "2px",
              opacity: current === 1 ? 0.5 : 1,
            }}
          >
            Previous
          </Button>

          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              minWidth: '100px',
              px: 2,
              borderRadius: '10px',
              backgroundColor: alpha(theme.palette.primary.main, 0.1),
              color: theme.palette.primary.main
            }}
          >
            <span style={{ color: theme.palette.primary.main }}>{current}</span>
            <span style={{ color: theme.palette.text.secondary, margin: '0 4px' }}>/</span>
            <span style={{ color: theme.palette.text.secondary }}>{filteredPageCount}</span>
            </Typography>

          <Button
            variant="outlined"
            color="primary"
            onClick={() => {
                setCurrent((prev) => Math.min(prev + 1, filteredPageCount));
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }}
              disabled={current === filteredPageCount || requirementValues.length < pageSize}
            endIcon={<ArrowForward />}
            sx={{
              borderRadius: "10px",
              textTransform: "none",
              fontWeight: 600,
              borderWidth: current === filteredPageCount || requirementValues.length < pageSize ? "1px" : "2px",
              opacity: current === filteredPageCount || requirementValues.length < pageSize ? 0.5 : 1,
            }}
          >
            Next
          </Button>
        </Paper>
      )}
    </Container>
  );
};

export default Requirement;