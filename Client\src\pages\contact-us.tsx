import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Container,
  Typography,
  Grid,
  TextField,
  Button,
  Box,
  Paper,
  Snackbar,
  Alert,
  CircularProgress,
  IconButton,
  useTheme,
  useMediaQuery,
  Divider,
  alpha,
} from '@mui/material';
import {
  LocationOn,
  Phone,
  Email,
  AccessTime,
  Send,
  CheckCircleOutline,
} from '@mui/icons-material';

// Brand Colors
const BRAND_COLORS = {
  red: '#d84030',
  blue: '#11418a',
};

// Motion components
//@ts-ignore
const MotionPaper = motion(Paper);
//@ts-ignore
const MotionContainer = motion(Container);
//@ts-ignore
const MotionTextField = motion(TextField);
//@ts-ignore
const MotionButton = motion(Button);

interface FormData {
  name: string;
  email: string;
  mobileNumber: string;
  message: string;
}

const initialFormData: FormData = {
  name: '',
  email: '',
  mobileNumber: '',
  message: '',
};

// Animation variants
const containerVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: { duration: 0.5 },
  },
};

export default function ContactUs() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  const [formData, setFormData] = useState<FormData>(initialFormData);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch('https://formbold.com/s/6MbX5', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        setIsSuccess(true);
        setSnackbar({
          open: true,
          message: 'Message sent successfully! We\'ll get back to you soon.',
          severity: 'success',
        });
        setFormData(initialFormData);
        setTimeout(() => setIsSuccess(false), 3000);
      } else {
        throw new Error('Failed to send message');
      }
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Failed to send message. Please try again.',
        severity: 'error',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSnackbarClose = () => {
    setSnackbar((prev) => ({ ...prev, open: false }));
  };

  const ContactInfo = ({ icon, text }: { icon: React.ReactNode; text: string }) => (
    <motion.div
      variants={itemVariants}
      whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}
    >
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        mb: 2,
        p: 2,
        borderRadius: 2,
        backgroundColor: alpha(BRAND_COLORS.blue, 0.1),
      }}>
        <IconButton sx={{ mr: 1, color: BRAND_COLORS.red }}>
          {icon}
        </IconButton>
        <Typography variant="body1" sx={{ color: BRAND_COLORS.blue }}>
          {text}
        </Typography>
      </Box>
    </motion.div>
  );

  return (
    <MotionContainer
      maxWidth="lg"
      sx={{ py: 6 }}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Box sx={{ mb: 6, textAlign: 'center' }}>
          <Typography 
            variant="h3" 
            component="h1" 
            gutterBottom
            sx={{ fontWeight: 700 }}
          >
            <Box component="span" sx={{ color: BRAND_COLORS.red }}>
              Contact
            </Box>{' '}
            <Box component="span" sx={{ color: BRAND_COLORS.blue }}>
              Us
            </Box>
          </Typography>
          <Typography 
            variant="h6" 
            color="text.secondary"
            sx={{ maxWidth: '600px', mx: 'auto', mt: 2 }}
          >
            We'd love to hear from you. Send us a message and we'll respond as soon as possible.
          </Typography>
        </Box>
      </motion.div>

      <Grid container spacing={4}>
        {/* Contact Form */}
        <Grid item xs={12} md={7}>
          <MotionPaper
            elevation={6}
            variants={itemVariants}
            whileHover={{ translateY: -8 }}
            sx={{
              p: 4,
              borderRadius: 4,
              height: '100%',
              background: '#ffffff',
            }}
          >
            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                {['name', 'email', 'mobileNumber'].map((field, index) => (
                  <Grid item xs={12} key={field}>
                    <MotionTextField
                      required
                      fullWidth
                      label={field.charAt(0).toUpperCase() + field.slice(1).replace('Number', '')}
                      name={field}
                      type={field === 'email' ? 'email' : field === 'mobileNumber' ? 'tel' : 'text'}
                      value={formData[field as keyof FormData]}
                      onChange={handleChange}
                      variants={itemVariants}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                          '&.Mui-focused fieldset': {
                            borderColor: BRAND_COLORS.blue,
                          },
                          '&:hover fieldset': {
                            borderColor: BRAND_COLORS.red,
                          },
                        },
                        '& .MuiInputLabel-root.Mui-focused': {
                          color: BRAND_COLORS.blue,
                        },
                      }}
                    />
                  </Grid>
                ))}
                <Grid item xs={12}>
                  <MotionTextField
                    required
                    fullWidth
                    label="Message"
                    name="message"
                    multiline
                    rows={4}
                    value={formData.message}
                    onChange={handleChange}
                    variants={itemVariants}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        '&.Mui-focused fieldset': {
                          borderColor: BRAND_COLORS.blue,
                        },
                        '&:hover fieldset': {
                          borderColor: BRAND_COLORS.red,
                        },
                      },
                      '& .MuiInputLabel-root.Mui-focused': {
                        color: BRAND_COLORS.blue,
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <MotionButton
                    type="submit"
                    fullWidth
                    variant="contained"
                    size="large"
                    disabled={isSubmitting}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    sx={{
                      borderRadius: 3,
                      py: 2,
                      bgcolor: BRAND_COLORS.blue,
                      '&:hover': {
                        bgcolor: alpha(BRAND_COLORS.blue, 0.9),
                      },
                    }}
                    endIcon={
                      isSuccess ? (
                        <CheckCircleOutline />
                      ) : isSubmitting ? (
                        <CircularProgress size={20} color="inherit" />
                      ) : (
                        <Send />
                      )
                    }
                  >
                    {isSuccess ? 'Sent!' : isSubmitting ? 'Sending...' : 'Send Message'}
                  </MotionButton>
                </Grid>
              </Grid>
            </form>
          </MotionPaper>
        </Grid>

        {/* Contact Information */}
        <Grid item xs={12} md={5}>
          <MotionPaper
            elevation={6}
            variants={itemVariants}
            whileHover={{ translateY: -8 }}
            sx={{
              p: 4,
              borderRadius: 4,
              height: '100%',
              bgcolor: '#ffffff',
            }}
          >
            <Typography variant="h5" gutterBottom sx={{ 
              color: BRAND_COLORS.blue,
              fontWeight: 600,
              mb: 4,
            }}>
              Contact Information
            </Typography>

            <Box sx={{ my: 4 }}>
              <ContactInfo icon={<LocationOn />} text="Near Bengali Square" />
              <ContactInfo icon={<Phone />} text="+91 93401 99672" />
              <ContactInfo icon={<Email />} text="<EMAIL>" />
            </Box>

            <Divider sx={{ my: 4, bgcolor: alpha(BRAND_COLORS.blue, 0.1) }} />

            <motion.div variants={itemVariants}>
              <Box sx={{
                p: 3,
                borderRadius: 2,
                bgcolor: alpha(BRAND_COLORS.blue, 0.1),
              }}>
                <Typography variant="h6" gutterBottom sx={{ 
                  color: BRAND_COLORS.blue,
                  display: 'flex',
                  alignItems: 'center',
                  fontWeight: 600,
                }}>
                  <AccessTime sx={{ mr: 1, color: BRAND_COLORS.red }} /> Business Hours
                </Typography>
                <Box sx={{ ml: 4 }}>
                  {[
                    'Monday - Friday: 9:00 AM - 6:00 PM',
                    'Saturday: 10:00 AM - 4:00 PM',
                    'Sunday: Closed'
                  ].map((text, index) => (
                    <Typography
                      key={index}
                      variant="body2"
                      sx={{
                        color: BRAND_COLORS.blue,
                        mb: 1,
                        fontWeight: index === 2 ? 600 : 400,
                      }}
                    >
                      {text}
                    </Typography>
                  ))}
                </Box>
              </Box>
            </motion.div>
          </MotionPaper>
        </Grid>
      </Grid>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          variant="filled"
          sx={{
            borderRadius: 2,
            bgcolor: snackbar.severity === 'success' ? BRAND_COLORS.blue : undefined,
          }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </MotionContainer>
  );
}