import { useState } from 'react';
import {
    useGetIdentity,
    useActiveAuthProvider,
} from "@refinedev/core";
import { useForm } from '@refinedev/react-hook-form';
import { useNavigate } from 'react-router-dom';
import Form from '../components/common/Form';
import { FieldValues } from "react-hook-form";
import { useParams } from 'react-router-dom';
import Resizer from "react-image-file-resizer";

interface PropertyImage {
    name: string;
    url: string;
}

export const CreateProperty = () => {
    const navigate = useNavigate();
    const authProvider = useActiveAuthProvider();
    const { data: user } = useGetIdentity({
        v3LegacyAuthProviderCompatible: Boolean(authProvider?.isLegacy),
    });

    const { id } = useParams();
    const [propertyImages, setPropertyImages] = useState<File[]>([]); // Store File objects array
    const {
        refineCore: { onFinish, formLoading },
        register,
        handleSubmit,
    } = useForm({
        refineCoreProps: {
            action: "create",
            resource: "properties",
            id: id,
        },
    });


    const resizeImage = (file: File): Promise<string> => { // Return Promise<string> for base64 URI
        return new Promise((resolve) => {
            Resizer.imageFileResizer(
                file,
                800, // Max width
                800, // Max height
                "JPEG", // Compression format
                80, // Quality (0-100)
                0, // Rotation
                (uri) => { // Resolve with base64 URI
                    //@ts-ignore
                    resolve(uri); // Resolve with the resized image URI
                },
                "base64" // Output type back to base64
            );
        });
    };

    // Updated handleImageChange function to store File objects array
    const handleImageChange = (files: FileList) => { // files is FileList
        const fileArray = Array.from(files); // Convert FileList to File array
        setPropertyImages(fileArray); // Store File objects array in state
    };

    const onFinishHandler = async (data: FieldValues) => {
        if (propertyImages.length === 0) return alert("Please select at least one image");
        if (propertyImages.length > 5) return alert("You can upload a maximum of 5 photos");

        // Resize images to Base64 URLs before sending
        const resizedImagePromises = propertyImages.map(async (file) => {
            try {
                return await resizeImage(file); // Resize each image and return base64 URI
            } catch (error) {
                console.error("Error resizing image:", error);
                return null; // Handle error as needed
            }
        });
        const resizedImageUrls = (await Promise.all(resizedImagePromises)).filter(url => url!== null) as string[]; // Filter out null URLs

        if (resizedImageUrls.length!== propertyImages.length) {
            return alert("Error resizing one or more images. Please try again."); // Handle resize errors
        }


        await onFinish({ // Send data as JSON object, not FormData
          ...data,
            photos: resizedImageUrls, // Send array of resized image URLs (base64 strings)
            email: user?.email?? '',
        });
    };

    return (
        <Form
            type='Create'
            register={register}
            onFinish={onFinish}
            formLoading={formLoading}
            handleSubmit={handleSubmit}
            //@ts-ignore
            handleImageChange={handleImageChange}
            onFinishHandler={onFinishHandler}
            propertyImages={propertyImages}
        />
    )
};