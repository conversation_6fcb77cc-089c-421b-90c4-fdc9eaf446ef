import React, { useState, useEffect } from "react";
import { useGetIdentity, useOne } from "@refinedev/core";
import { useForm, } from "@refinedev/react-hook-form";
import { FieldValues } from "react-hook-form";
import Form from "../components/common/Form";
import { useActiveAuthProvider } from "@refinedev/core";
import { useParams } from "react-router-dom";

// Define ImageType interface
type ImageType =
    | { type: 'url'; source: string; id?: string }
    | { type: 'file'; source: File };

const EditProperty = () => {
    const authProvider = useActiveAuthProvider();
    const { data: user } = useGetIdentity({
        v3LegacyAuthProviderCompatible: Boolean(authProvider?.isLegacy),
    });
    const { id } = useParams();
    console.log(id);
    const [propertyImages, setPropertyImages] = useState<ImageType[]>([]); // State to hold ImageType array
    const { data: propertyData, isLoading: propertyLoading } = useOne({
        resource: "properties",
        id: id,
    });

    useEffect(() => {
        if (propertyData?.data) {
            // Transform existing image URLs to ImageType format
            const initialImages: ImageType[] = (propertyData.data.photos as string[] || [propertyData.data.photo as string])
                ?.filter(Boolean)
                .map(photo => ({
                    type: 'url',
                    source: photo || "",
                    id: `existing-image-${Date.now()}` // Optional: Add IDs for existing images
                })) || [];
            setPropertyImages(initialImages);
        }
    }, [propertyData]);


    const {
        refineCore: { onFinish, formLoading },
        register,
        handleSubmit,
    } = useForm({
        refineCoreProps: {
            action: "edit",
            resource: "properties",
            id: id,
        },
        defaultValues: {
            photos: [] // Initialize photos to be an empty array in form default values
        }
    });

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = e.target.files;
        if (files) {
            const filesArray = Array.from(files);
            if (filesArray.length > 5) {
                alert("You can upload a maximum of 5 photos.");
                return;
            }

            const validImageFiles = filesArray.filter(file => {
                if (file.size > 5 * 1024 * 1024) {
                    alert(`File size must be less than 5MB for ${file.name}`);
                    return false;
                }
                if (!file.type.startsWith("image/")) {
                    alert(`Please upload an image file for ${file.name}`);
                    return false;
                }
                return true;
            });

            if (validImageFiles.length > 0) {
                const newImages: ImageType[] = validImageFiles.map(file => ({ type: 'file', source: file })); // Create ImageType for new files
                setPropertyImages(prevImages => [...prevImages, ...newImages]); // Add new ImageTypes to existing state
            }
        }
    };


    const onFinishHandler = async (data: FieldValues) => {
        if (propertyImages.length === 0) {
            return alert("Please upload at least one property image");
        }

        // Extract URLs from both existing and newly uploaded images
        const photoUrls = propertyImages.map(image => image.type === 'url' ? image.source : URL.createObjectURL(image.source as File));


        await onFinish({
            ...data,
            //@ts-ignore
            photos: photoUrls, // Send array of photo URLs
            email: user?.email,
        });
    };

    const handleRemoveImage = (indexToRemove: number) => {
        setPropertyImages(images => images.filter((_, index) => index !== indexToRemove));
    };


    return (
        <Form
            type="Edit"
            register={register}
            onFinish={onFinish}
            formLoading={formLoading}
            handleSubmit={handleSubmit}
            //@ts-ignore
            handleImageChange={handleImageChange}
            onFinishHandler={onFinishHandler}
            //@ts-ignore
            propertyImages={propertyImages} // Pass ImageType array to Form
            handleRemoveImage={handleRemoveImage}
            initialVisibility={propertyData?.data?.isPublic ?? true} // Pass current visibility state
        />
    );
};

export default EditProperty;