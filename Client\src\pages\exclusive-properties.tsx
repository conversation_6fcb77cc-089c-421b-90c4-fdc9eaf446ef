import { useState, useEffect } from 'react';
import {
  Box,
  <PERSON>pography,
  Grid,
  Button,
  Container,
  useTheme,
  useMediaQuery,
  alpha,
  Fade,
  Paper,
  Alert,
  Snackbar,
  Skeleton,
} from '@mui/material';
import {
  Add as AddIcon,
} from '@mui/icons-material';
import ExclusivePropertyCard, { ExclusiveProperty } from '../components/common/ExclusivePropertyCard';
import { useNavigate } from 'react-router-dom';
import { useGetIdentity } from '@refinedev/core';
import axios from 'axios';

// Using ExclusiveProperty type imported from the component

export const ExclusiveProperties = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  // Use isMobile for responsive design decisions
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { data: user } = useGetIdentity<{ email: string, isSuperUser?: boolean }>();

  const [properties, setProperties] = useState<ExclusiveProperty[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSuperUser, setIsSuperUser] = useState(false);
  const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:8080';

  // Check if user is a super user
  useEffect(() => {
    const checkSuperUser = async () => {
      if (!user?.email) return;

      try {
        const response = await axios.get(`${apiUrl}/api/v1/exclusive-properties/check-superuser/${user.email}`);
        setIsSuperUser(response.data.isSuperUser);
      } catch (err) {
        // Silently handle error - user will not have super user privileges
      }
    };

    checkSuperUser();
  }, [user?.email, apiUrl]);

  // Fetch exclusive properties
  useEffect(() => {
    const fetchProperties = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`${apiUrl}/api/v1/exclusive-properties`);

        // Format the price and commission for display
        const formattedProperties = response.data.map((property: ExclusiveProperty) => ({
          ...property,
          formattedPrice: `₹${property.priceRange.min.toLocaleString('en-IN')} - ₹${property.priceRange.max.toLocaleString('en-IN')}`,
          formattedCommission: property.commissionRange ?
            `${property.commissionRange.min}% - ${property.commissionRange.max}%` :
            'Commission not specified',
          viewCount: property.viewCount || 0
        }));

        setProperties(formattedProperties);
      } catch (err) {
        setError("Failed to load exclusive properties. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchProperties();
  }, [apiUrl]);

  // Function to handle button click and navigate to the form page
  const handleOpenExclusiveForm = () => {
    if (!isSuperUser) {
      setError("Only authorized users can create exclusive properties. Please contact the administrator if you need access.");
      return;
    }
    navigate('/create-exclusive-property');
  };


  return (
    <Container maxWidth="lg" sx={{ py: { xs: 3, sm: 5, md: 6 } }}>
      {error && (
        <Snackbar open={!!error} autoHideDuration={6000} onClose={() => setError(null)}>
          <Alert severity="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        </Snackbar>
      )}

      <Fade in timeout={800}>
        <Box>
          {/* Header Section with Modern Design */}
          <Box
            sx={{
              textAlign: 'center',
              mb: { xs: 3, sm: 5 },
              position: 'relative',
              overflow: 'hidden',
              borderRadius: { xs: 2, sm: 3 },
              p: { xs: 3, sm: 5 },
              background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.primary.main, 0.2)} 100%)`,
              boxShadow: `0 10px 40px ${alpha(theme.palette.common.black, 0.05)}`,
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                backgroundImage: 'url("data:image/svg+xml,%3Csvg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z" fill="${encodeURIComponent(theme.palette.primary.main)}" fill-opacity="0.05" fill-rule="evenodd"/%3E%3C/svg%3E")',
                opacity: 0.3,
              }
            }}
          >
            <Typography
              variant="h3"
              component="h1"
              sx={{
                fontWeight: 800,
                fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
                mb: 2,
                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                textShadow: `0 2px 10px ${alpha(theme.palette.primary.main, 0.2)}`,
                position: 'relative',
              }}
            >
              Exclusive Properties
            </Typography>
            <Typography
              variant="subtitle1"
              sx={{
                color: 'text.secondary',
                maxWidth: '700px',
                mx: 'auto',
                mb: 3,
                fontSize: { xs: '1rem', sm: '1.1rem' },
                lineHeight: 1.6,
                position: 'relative',
              }}
            >
              Discover our curated selection of premium properties, featuring unique architectural designs and exceptional locations.
            </Typography>

            {/* Add New Property Button - Only visible to super users */}
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
              {isSuperUser ? (
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<AddIcon />}
                  onClick={handleOpenExclusiveForm}
                  sx={{
                    borderRadius: 30,
                    py: { xs: 1.2, sm: 1.5 },
                    px: { xs: 2.5, sm: 3.5 },
                    textTransform: 'none',
                    fontWeight: 600,
                    fontSize: { xs: '0.9rem', sm: '1rem' },
                    boxShadow: `0 8px 20px ${alpha(theme.palette.primary.main, 0.3)}`,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-3px)',
                      boxShadow: `0 12px 24px ${alpha(theme.palette.primary.main, 0.4)}`,
                    },
                    '&:active': {
                      transform: 'translateY(0)',
                    }
                  }}
                >
                  Add New Exclusive Property
                </Button>
              ) : null}
            </Box>
          </Box>

          {/* Loading State with Skeleton */}
          {loading ? (
            <Box sx={{ py: 4 }}>
              <Grid container spacing={3}>
                {[1, 2, 3, 4, 5, 6].map((item) => (
                  <Grid item xs={12} sm={6} md={4} key={`skeleton-${item}`}>
                    <Skeleton
                      variant="rectangular"
                      width="100%"
                      height={240}
                      sx={{ borderRadius: 3, mb: 1 }}
                    />
                    <Skeleton variant="text" width="80%" height={30} sx={{ mb: 0.5 }} />
                    <Skeleton variant="text" width="60%" height={24} sx={{ mb: 1 }} />
                    <Skeleton variant="text" width="40%" height={24} sx={{ mb: 1 }} />
                    <Skeleton variant="rectangular" width="100%" height={40} sx={{ borderRadius: 1 }} />
                  </Grid>
                ))}
              </Grid>
            </Box>
          ) : (
            <>
              {/* Properties Grid with Improved Mobile Layout */}
              <Grid container spacing={{ xs: 1.5, sm: 2 }}>
                {properties.map((property, index) => (
                  <Grid item xs={12} sm={6} md={4} lg={3} key={property._id}>
                    <Fade in timeout={400 + (index * 50)}>
                      <Box>
                        <ExclusivePropertyCard
                          property={property}
                          index={index}
                          animationDelay={0.05 * index}
                        />
                      </Box>
                    </Fade>
                  </Grid>
                ))}

              </Grid>

              {/* Enhanced Empty State with Visual Elements */}
              {properties.length === 0 && !loading && (
                <Fade in timeout={800}>
                  <Paper
                    elevation={0}
                    sx={{
                      p: { xs: 3, sm: 5 },
                      textAlign: 'center',
                      borderRadius: { xs: 2, sm: 3 },
                      backgroundColor: alpha(theme.palette.background.paper, 0.7),
                      border: `1px dashed ${alpha(theme.palette.primary.main, 0.3)}`,
                      boxShadow: `0 10px 40px ${alpha(theme.palette.common.black, 0.03)}`,
                      position: 'relative',
                      overflow: 'hidden',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: '100%',
                        backgroundImage: 'url("data:image/svg+xml,%3Csvg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z" fill="${encodeURIComponent(theme.palette.primary.main)}" fill-opacity="0.05" fill-rule="evenodd"/%3E%3C/svg%3E")',
                        opacity: 0.3,
                      }
                    }}
                  >
                    <Box sx={{ position: 'relative', zIndex: 1 }}>
                      <Box
                        component="img"
                        src="https://cdn-icons-png.flaticon.com/512/6460/6460112.png"
                        alt="No properties"
                        sx={{
                          width: { xs: 80, sm: 100 },
                          height: { xs: 80, sm: 100 },
                          opacity: 0.7,
                          mb: 2,
                          filter: 'grayscale(0.3)'
                        }}
                      />
                      <Typography
                        variant="h5"
                        sx={{
                          color: 'text.primary',
                          fontWeight: 600,
                          mb: 1,
                          fontSize: { xs: '1.25rem', sm: '1.5rem' }
                        }}
                      >
                        No exclusive properties available
                      </Typography>
                      <Typography
                        variant="body1"
                        color="text.secondary"
                        sx={{
                          maxWidth: '500px',
                          mx: 'auto',
                          mb: 3,
                          fontSize: { xs: '0.9rem', sm: '1rem' }
                        }}
                      >
                        Our team is working on adding new premium listings. Please check back later for exclusive properties.
                      </Typography>
                      <Button
                        variant="outlined"
                        color="primary"
                        onClick={() => navigate('/')}
                        sx={{
                          borderRadius: 30,
                          px: 3,
                          py: 1,
                          textTransform: 'none',
                          fontWeight: 500
                        }}
                      >
                        Browse Regular Properties
                      </Button>
                    </Box>
                  </Paper>
                </Fade>
              )}
            </>
          )}
        </Box>
      </Fade>
    </Container>
  );
};