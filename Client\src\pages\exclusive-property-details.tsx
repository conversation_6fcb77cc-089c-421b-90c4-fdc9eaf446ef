import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useGetIdentity } from '@refinedev/core';
import axios from 'axios';
import {
  Box,
  Container,
  Typography,
  Grid,
  Paper,
  Button,
  Stack,
  CircularProgress,
  Alert,
  Snackbar,
  useTheme,
  alpha,
  IconButton,
  Divider,
  Modal,
  Backdrop,
  Fade,
  Chip,
  Zoom,
  Fab,
} from '@mui/material';
import {
  LocationOn,
  CalendarToday,
  SquareFoot,
  Apartment,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ArrowBack,
  Description,
  ArrowForwardIos,
  ArrowBackIos,
  Fullscreen,
  Visibility as VisibilityIcon,
  Phone,
  Call,
  WhatsApp,
  OpenInNew,
  KeyboardArrowUp,
  CurrencyRupee,
  Info,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import MetaTags from '../components/common/MetaTags';
import ShareButton from '../components/common/ShareButton';

// Define the ExclusiveProperty type
interface ExclusiveProperty {
  _id: string;
  title: string;
  description: string;
  location: string;
  propertyType: string;
  priceRange: {
    min: number;
    max: number;
  };
  typology: string;
  commissionRange: {
    min: number;
    max: number;
  };
  possessionDate: string;
  configuration: string;
  reraNumber: string;
  totalArea: number;
  paymentPlan: {
    installment: string;
    percentage: number;
    description?: string;
  }[];
  totalBookings: number;
  websiteLink?: string;
  brochureLink?: string;
  gpsLocation?: string;
  floorPlanLinks?: string[];
  images: string[];
  creator: {
    _id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  createdAt: string;
  updatedAt: string;
  formattedPrice?: string;
  formattedCommission?: string;
  formattedPossessionDate?: string;
}

const ExclusivePropertyDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const theme = useTheme();
  const { data: user } = useGetIdentity<{ email: string, isSuperUser?: boolean }>();

  // Ref for scrolling to top
  const topRef = useRef<HTMLDivElement>(null);

  const [property, setProperty] = useState<ExclusiveProperty | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isSuperUser, setIsSuperUser] = useState(false);
  const [deleteConfirm, setDeleteConfirm] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [shareUrl, setShareUrl] = useState('');
  const [viewCount, setViewCount] = useState(0);
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const [modalImageIndex, setModalImageIndex] = useState(0);
  const [showBackToTop, setShowBackToTop] = useState(false);
  const [imageAspectRatios, setImageAspectRatios] = useState<number[]>([]);
  const [imageLoaded, setImageLoaded] = useState(false);

  const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:8080';

  // Check if user is a super user
  useEffect(() => {
    const checkSuperUser = async () => {
      if (!user?.email) return;

      try {
        const response = await axios.get(`${apiUrl}/api/v1/exclusive-properties/check-superuser/${user.email}`);
        setIsSuperUser(response.data.isSuperUser);
      } catch (err) {
        console.error("Error checking super user status:", err);
      }
    };

    checkSuperUser();
  }, [user?.email, apiUrl]);

  // Handle scroll events for back to top button
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setShowBackToTop(true);
      } else {
        setShowBackToTop(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });

    // Alternative using ref
    if (topRef.current) {
      topRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Function to calculate image aspect ratios
  const calculateImageAspectRatios = (images: string[]) => {
    const aspectRatios: number[] = [];
    let loadedCount = 0;

    // Reset image loaded state
    setImageLoaded(false);

    images.forEach((imageUrl, index) => {
      const img = new Image();
      img.onload = () => {
        // Calculate aspect ratio (width / height)
        const aspectRatio = img.width / img.height;
        aspectRatios[index] = aspectRatio;

        loadedCount++;
        if (loadedCount === images.length) {
          setImageAspectRatios(aspectRatios);
          setImageLoaded(true);
        }
      };

      img.onerror = () => {
        // Use default aspect ratio (4/3) for failed images
        aspectRatios[index] = 4/3;

        loadedCount++;
        if (loadedCount === images.length) {
          setImageAspectRatios(aspectRatios);
          setImageLoaded(true);
        }
      };

      img.src = imageUrl;
    });
  };

  // Fetch property details
  useEffect(() => {
    const fetchPropertyDetails = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const response = await axios.get(`${apiUrl}/api/v1/exclusive-properties/${id}`);

        // Format dates and numbers for display
        const propertyData = {
          ...response.data,
          formattedPrice: `₹${response.data.priceRange.min.toLocaleString('en-IN')} - ₹${response.data.priceRange.max.toLocaleString('en-IN')}`,
          formattedCommission: `${response.data.commissionRange.min}% - ${response.data.commissionRange.max}%`,
          formattedPossessionDate: new Date(response.data.possessionDate).toLocaleDateString('en-IN', {
            year: 'numeric',
            month: 'long'
          })
        };

        setProperty(propertyData);

        // Set view count from the API response
        setViewCount(response.data.viewCount || 0);

        // Set the share URL - use slug if available, otherwise use ID
        const baseUrl = window.location.origin;
        if (propertyData.slug) {
          setShareUrl(`${baseUrl}/exclusive/${propertyData.slug}`);
        } else {
          setShareUrl(`${baseUrl}/exclusive/${id}`);
        }

        // Calculate aspect ratios for images
        if (propertyData.images && propertyData.images.length > 0) {
          calculateImageAspectRatios(propertyData.images);
        }

        // Track view
        try {
          await axios.post(`${apiUrl}/api/v1/exclusive-properties/${id}/track-view`);
        } catch (trackErr) {
          console.error("Error tracking view:", trackErr);
        }
      } catch (err) {
        console.error("Error fetching property details:", err);
        setError("Failed to load property details. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchPropertyDetails();
  }, [id, apiUrl]);

  // Handle property deletion
  const handleDelete = async () => {
    if (!id || !user?.email) return;

    if (!isSuperUser) {
      setError("Only authorized users can delete exclusive properties. Please contact the administrator if you need access.");
      setDeleteConfirm(false);
      return;
    }

    try {
      setLoading(true);
      await axios.delete(`${apiUrl}/api/v1/exclusive-properties/${id}`, {
        data: { email: user.email }
      });

      setSuccess("Property deleted successfully!");

      // Redirect after successful deletion
      setTimeout(() => {
        navigate('/exclusive');
      }, 2000);
    } catch (err: any) {
      console.error("Error deleting property:", err);
      setError(err.response?.data?.message || "Failed to delete property. Please try again later.");
      setDeleteConfirm(false);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!property) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error">
          Property not found or has been removed.
        </Alert>
        <Button
          variant="contained"
          startIcon={<ArrowBack />}
          onClick={() => navigate('/exclusive')}
          sx={{ mt: 2 }}
        >
          Back to Exclusive Properties
        </Button>
      </Container>
    );
  }

  // Set meta tags for sharing
  const metaDescription =
    `${property.title} - ${property.propertyType} in ${property.location}. ${property.typology} with ${property.totalArea} sq ft. Price range: ${property.formattedPrice || ''}`;

  // Ensure mainImage is always a string
  const mainImage = property.images && property.images.length > 0
    ? property.images[0]
    : 'https://via.placeholder.com/800x600?text=No+Image+Available';

  return (
    <Container
      maxWidth="lg"
      sx={{
        py: { xs: 4, sm: 6 },
        position: 'relative'
      }}
    >
      {/* Reference for scroll to top */}
      <Box ref={topRef} sx={{ position: 'absolute', top: 0 }} />

      {/* Back to Top Button */}
      <Zoom in={showBackToTop}>
        <Fab
          color="primary"
          size="small"
          aria-label="scroll back to top"
          onClick={scrollToTop}
          sx={{
            position: 'fixed',
            bottom: 16,
            right: 16,
            zIndex: 1000,
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
          }}
        >
          <KeyboardArrowUp />
        </Fab>
      </Zoom>

      {/* Enhanced Dynamic Meta Tags for better social sharing */}
      <MetaTags
        title={property.title}
        description={metaDescription}
        imageUrl={mainImage}
        url={shareUrl}
        type="article"
        price={property.formattedPrice}
        location={property.location}
        propertyType={property.propertyType}
      />

      {error && (
        <Snackbar open={!!error} autoHideDuration={6000} onClose={() => setError(null)}>
          <Alert severity="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        </Snackbar>
      )}

      {success && (
        <Snackbar open={!!success} autoHideDuration={6000} onClose={() => setSuccess(null)}>
          <Alert severity="success" onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        </Snackbar>
      )}

      {/* Delete Confirmation Dialog */}
      {deleteConfirm && (
        <Snackbar open={deleteConfirm} autoHideDuration={6000} onClose={() => setDeleteConfirm(false)}>
          <Alert
            severity="warning"
            action={
              <>
                <Button color="inherit" size="small" onClick={() => setDeleteConfirm(false)}>
                  Cancel
                </Button>
                <Button color="error" size="small" onClick={handleDelete}>
                  Delete
                </Button>
              </>
            }
          >
            Are you sure you want to delete this property? This action cannot be undone.
          </Alert>
        </Snackbar>
      )}

      {/* Back Button with Mobile Optimization */}
      <motion.div
        initial={{ x: -20, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <Button
          variant="outlined"
          startIcon={<ArrowBack />}
          onClick={() => navigate('/exclusive')}
          sx={{
            mb: 3,
            borderRadius: { xs: 20, sm: 4 },
            py: { xs: 1, sm: 1.2 },
            px: { xs: 2, sm: 3 },
            textTransform: 'none',
            fontSize: { xs: '0.8rem', sm: '0.9rem' },
            boxShadow: `0 2px 8px ${alpha(theme.palette.primary.main, 0.1)}`,
            '&:hover': {
              boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.15)}`,
              transform: 'translateX(-5px)'
            },
            transition: 'all 0.2s ease'
          }}
        >
          Back to Exclusive Properties
        </Button>
      </motion.div>

      {/* Sticky Header with Key Property Info - Visible on Scroll */}
      <Box
        component={motion.div}
        initial={{ y: -100, opacity: 0 }}
        animate={{
          y: showBackToTop ? 0 : -100,
          opacity: showBackToTop ? 1 : 0
        }}
        transition={{ duration: 0.3 }}
        sx={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 999,
          bgcolor: 'background.paper',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
          py: 1.5,
          px: 2,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton
            size="small"
            onClick={() => navigate('/exclusive')}
            sx={{ mr: 1.5 }}
          >
            <ArrowBack />
          </IconButton>
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 600,
              fontSize: { xs: '0.9rem', sm: '1rem' },
              maxWidth: { xs: '150px', sm: '250px', md: '400px' },
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}
          >
            {property.title}
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Chip
            label={property.formattedPrice}
            color="primary"
            size="small"
            sx={{ fontWeight: 600 }}
          />
          <ShareButton
            propertyId={property._id}
            propertyTitle={property.title}
            propertyImage={mainImage}
            propertyLocation={property.location}
            propertyPrice={property.formattedPrice}
            shareUrl={shareUrl}
            apiUrl={apiUrl}
            variant="icon"
            size="small"
          />
        </Box>
      </Box>

      <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>
        {/* Left Column - Images and Description */}
        <Grid item xs={12} md={7}>
          {/* Image Gallery Section */}
          {property.images && property.images.length > 0 ? (
            <Paper
              elevation={3}
              sx={{
                borderRadius: { xs: 2, sm: 3 },
                overflow: 'hidden',
                mb: { xs: 2, sm: 3 },
                boxShadow: `0 10px 30px ${alpha(theme.palette.common.black, 0.08)}`,
                p: { xs: 1, sm: 2 },
              }}
            >
              {/* Main Image Container */}
              <Box
                sx={{
                  position: 'relative',
                  mb: { xs: 1, sm: 2 },
                  overflow: 'hidden',
                  borderRadius: { xs: 1, sm: 2 },
                  boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.1)}`,
                }}
              >
                {/* Image Container with Dynamic Aspect Ratio */}
                <Box
                  component={motion.div}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                  sx={{
                    position: 'relative',
                    width: '100%',
                    // Use dynamic aspect ratio if available, otherwise use responsive container
                    ...(imageAspectRatios[selectedImageIndex]
                      ? {
                          paddingTop: `${(1 / (imageAspectRatios[selectedImageIndex] || 1.33)) * 100}%`,
                        }
                      : {
                          height: { xs: '250px', sm: '350px', md: '450px' },
                        }
                    ),
                    backgroundColor: alpha(theme.palette.primary.light, 0.05),
                  }}
                >
                  {/* Loading Skeleton */}
                  {!imageLoaded && (
                    <Box
                      sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: '100%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        bgcolor: alpha(theme.palette.background.paper, 0.1),
                      }}
                    >
                      <CircularProgress size={40} thickness={4} />
                    </Box>
                  )}

                  {/* Main Image */}
                  <Box
                    component="img"
                    src={property.images[selectedImageIndex]}
                    alt={`${property.title} - Image ${selectedImageIndex + 1}`}
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '100%',
                      objectFit: (imageAspectRatios[selectedImageIndex] || 1) > 1.5 ? 'contain' : 'cover',
                      objectPosition: 'center',
                      display: 'block',
                      transition: 'transform 0.5s ease, opacity 0.3s ease',
                      cursor: 'pointer',
                      opacity: imageLoaded ? 1 : 0.3,
                      '&:hover': {
                        transform: 'scale(1.02)',
                      }
                    }}
                    onClick={() => {
                      setModalImageIndex(selectedImageIndex);
                      setIsImageModalOpen(true);
                    }}
                    onLoad={() => {
                      // Ensure image is marked as loaded when directly loaded from cache
                      if (!imageLoaded && selectedImageIndex === 0) {
                        setImageLoaded(true);
                      }
                    }}
                  />

                  {/* Navigation Buttons - Improved for Mobile */}
                  {property.images.length > 1 && (
                    <>
                      {/* Previous Button */}
                      <IconButton
                        size="small"
                        sx={{
                          position: 'absolute',
                          left: { xs: 4, sm: 8 },
                          top: '50%',
                          transform: 'translateY(-50%)',
                          bgcolor: alpha(theme.palette.background.paper, 0.8),
                          boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
                          width: { xs: 32, sm: 40 },
                          height: { xs: 32, sm: 40 },
                          '&:hover': {
                            bgcolor: alpha(theme.palette.background.paper, 0.95),
                            transform: 'translateY(-50%) scale(1.1)',
                          },
                          transition: 'all 0.2s ease',
                        }}
                        onClick={() => {
                          setSelectedImageIndex(prev =>
                            prev === 0 ? property.images.length - 1 : prev - 1
                          );
                        }}
                      >
                        <ArrowBackIos sx={{ fontSize: { xs: '0.9rem', sm: '1.2rem' }, ml: 0.5 }} />
                      </IconButton>

                      {/* Next Button */}
                      <IconButton
                        size="small"
                        sx={{
                          position: 'absolute',
                          right: { xs: 4, sm: 8 },
                          top: '50%',
                          transform: 'translateY(-50%)',
                          bgcolor: alpha(theme.palette.background.paper, 0.8),
                          boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
                          width: { xs: 32, sm: 40 },
                          height: { xs: 32, sm: 40 },
                          '&:hover': {
                            bgcolor: alpha(theme.palette.background.paper, 0.95),
                            transform: 'translateY(-50%) scale(1.1)',
                          },
                          transition: 'all 0.2s ease',
                        }}
                        onClick={() => {
                          setSelectedImageIndex(prev =>
                            prev === property.images.length - 1 ? 0 : prev + 1
                          );
                        }}
                      >
                        <ArrowForwardIos sx={{ fontSize: { xs: '0.9rem', sm: '1.2rem' } }} />
                      </IconButton>

                      {/* Fullscreen Button */}
                      <IconButton
                        size="small"
                        sx={{
                          position: 'absolute',
                          right: { xs: 4, sm: 8 },
                          top: { xs: 4, sm: 8 },
                          bgcolor: alpha(theme.palette.background.paper, 0.8),
                          boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
                          width: { xs: 32, sm: 40 },
                          height: { xs: 32, sm: 40 },
                          '&:hover': {
                            bgcolor: alpha(theme.palette.background.paper, 0.95),
                            transform: 'scale(1.1)',
                          },
                          transition: 'all 0.2s ease',
                        }}
                        onClick={() => {
                          setModalImageIndex(selectedImageIndex);
                          setIsImageModalOpen(true);
                        }}
                      >
                        <Fullscreen sx={{ fontSize: { xs: '0.9rem', sm: '1.2rem' } }} />
                      </IconButton>

                      {/* Image Counter - Improved Design */}
                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: { xs: 4, sm: 8 },
                          right: { xs: 4, sm: 8 },
                          bgcolor: alpha(theme.palette.background.paper, 0.8),
                          px: { xs: 1, sm: 1.5 },
                          py: { xs: 0.3, sm: 0.5 },
                          borderRadius: 20,
                          fontSize: { xs: '0.7rem', sm: '0.875rem' },
                          fontWeight: 500,
                          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                          color: theme.palette.text.primary,
                        }}
                      >
                        {selectedImageIndex + 1} / {property.images.length}
                      </Box>
                    </>
                  )}
                </Box>

                {/* Enhanced Thumbnails - Responsive to Image Dimensions */}
                <Box
                  component={motion.div}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  sx={{
                    display: 'flex',
                    flexWrap: 'nowrap',
                    gap: { xs: 0.8, sm: 1.2 },
                    justifyContent: 'flex-start',
                    overflowX: 'auto',
                    px: { xs: 1.5, sm: 2 },
                    pb: { xs: 1.5, sm: 2 },
                    pt: 0.5,
                    '::-webkit-scrollbar': {
                      height: '6px',
                    },
                    '::-webkit-scrollbar-track': {
                      background: alpha(theme.palette.primary.main, 0.05),
                      borderRadius: 3,
                    },
                    '::-webkit-scrollbar-thumb': {
                      background: alpha(theme.palette.primary.main, 0.2),
                      borderRadius: 3,
                      '&:hover': {
                        background: alpha(theme.palette.primary.main, 0.3),
                      },
                    },
                    // Add snap scrolling for better mobile experience
                    scrollSnapType: 'x mandatory',
                    WebkitOverflowScrolling: 'touch',
                  }}
                >
                  {property.images.map((image, index) => {
                    // Calculate optimal dimensions based on aspect ratio
                    const aspectRatio = imageAspectRatios[index] || 1;
                    const isWide = aspectRatio > 1.3;
                    const isVeryWide = aspectRatio > 2;
                    const isTall = aspectRatio < 0.8;

                    // Adjust width based on aspect ratio
                    let width = { xs: 70, sm: 90 };
                    if (isVeryWide) {
                      width = { xs: 90, sm: 120 };
                    } else if (isWide) {
                      width = { xs: 80, sm: 100 };
                    } else if (isTall) {
                      width = { xs: 60, sm: 75 };
                    }

                    return (
                      <Box
                        component={motion.div}
                        key={index}
                        whileHover={{ scale: 1.05, y: -3 }}
                        whileTap={{ scale: 0.95 }}
                        sx={{
                          position: 'relative',
                          width: width,
                          height: { xs: 70, sm: 90 },
                          borderRadius: { xs: 1.5, sm: 2 },
                          overflow: 'hidden',
                          flexShrink: 0,
                          cursor: 'pointer',
                          boxShadow: index === selectedImageIndex
                            ? `0 4px 12px ${alpha(theme.palette.primary.main, 0.3)}`
                            : `0 2px 8px ${alpha(theme.palette.common.black, 0.1)}`,
                          border: index === selectedImageIndex
                            ? `2px solid ${theme.palette.primary.main}`
                            : `2px solid transparent`,
                          transition: 'all 0.2s ease',
                          scrollSnapAlign: 'start',
                          // Add subtle gradient overlay for better text contrast
                          '&::after': index === selectedImageIndex ? {
                            content: '""',
                            position: 'absolute',
                            bottom: 0,
                            left: 0,
                            right: 0,
                            height: '30%',
                            background: `linear-gradient(to top, ${alpha(theme.palette.primary.main, 0.2)}, transparent)`,
                            zIndex: 1,
                          } : {},
                        }}
                        onClick={() => {
                          setSelectedImageIndex(index);
                        }}
                        onDoubleClick={() => {
                          setModalImageIndex(index);
                          setIsImageModalOpen(true);
                        }}
                      >
                        <Box
                          component="img"
                          src={image}
                          alt={`${property.title} - Image ${index + 1}`}
                          sx={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover',
                            opacity: index === selectedImageIndex ? 1 : 0.7,
                            transition: 'opacity 0.2s ease',
                            '&:hover': {
                              opacity: 0.9,
                            }
                          }}
                        />

                        {/* Image number indicator */}
                        <Typography
                          variant="caption"
                          sx={{
                            position: 'absolute',
                            bottom: 2,
                            right: 2,
                            zIndex: 2,
                            fontSize: '0.65rem',
                            fontWeight: 600,
                            color: 'white',
                            bgcolor: alpha(theme.palette.common.black, 0.5),
                            px: 0.8,
                            py: 0.2,
                            borderRadius: 5,
                            display: index === selectedImageIndex ? 'block' : 'none',
                          }}
                        >
                          {index + 1}
                        </Typography>
                      </Box>
                    );
                  })}
                </Box>
              </Box>
            </Paper>
          ) : (
            <Paper
              elevation={3}
              sx={{
                borderRadius: { xs: 2, sm: 3 },
                overflow: 'hidden',
                mb: { xs: 2, sm: 3 },
                boxShadow: `0 10px 30px ${alpha(theme.palette.common.black, 0.08)}`,
              }}
            >
              <Box
                sx={{
                  height: { xs: 300, sm: 400 },
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  bgcolor: alpha(theme.palette.primary.light, 0.05),
                  flexDirection: 'column',
                  gap: 2,
                }}
              >
                <Box
                  component="img"
                  src="https://cdn-icons-png.flaticon.com/512/1829/1829552.png"
                  alt="No images"
                  sx={{
                    width: { xs: 60, sm: 80 },
                    height: { xs: 60, sm: 80 },
                    opacity: 0.5
                  }}
                />
                <Typography variant="h6" color="text.secondary">
                  No images available
                </Typography>
              </Box>
            </Paper>
          )}

          {/* Property Description - Mobile Optimized */}
          <Paper
            elevation={2}
            sx={{
              p: { xs: 2, sm: 3 },
              borderRadius: { xs: 2, sm: 3 },
              mb: { xs: 2, sm: 3 },
              boxShadow: `0 10px 30px ${alpha(theme.palette.common.black, 0.05)}`,
            }}
          >
            <Typography
              variant="h6"
              gutterBottom
              sx={{
                fontSize: { xs: '1.1rem', sm: '1.25rem' },
                fontWeight: 600,
                color: theme.palette.primary.main,
                display: 'flex',
                alignItems: 'center',
                gap: 1,
              }}
            >
              <Description fontSize="small" /> Description
            </Typography>
            <Typography
              variant="body1"
              sx={{
                whiteSpace: 'pre-line',
                fontSize: { xs: '0.9rem', sm: '1rem' },
                lineHeight: 1.6,
                color: alpha(theme.palette.text.primary, 0.9),
              }}
            >
              {property.description}
            </Typography>
          </Paper>

          {/* Payment Plan - Mobile Optimized */}
          <Paper
            elevation={2}
            sx={{
              p: { xs: 2, sm: 3 },
              borderRadius: { xs: 2, sm: 3 },
              mb: { xs: 3, md: 0 },
              boxShadow: `0 10px 30px ${alpha(theme.palette.common.black, 0.05)}`,
            }}
          >
            <Typography
              variant="h6"
              gutterBottom
              sx={{
                fontSize: { xs: '1.1rem', sm: '1.25rem' },
                fontWeight: 600,
                color: theme.palette.primary.main,
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                mb: 2,
              }}
            >
              <CurrencyRupee fontSize="small" /> Payment Plan
            </Typography>
            {property.paymentPlan && property.paymentPlan.length > 0 ? (
              <Box>
                {property.paymentPlan.map((plan, index) => (
                  <Box
                    key={index}
                    sx={{
                      mb: { xs: 1.5, sm: 2 },
                      pb: { xs: 1.5, sm: 2 },
                      borderBottom: index < property.paymentPlan.length - 1
                        ? `1px solid ${alpha(theme.palette.divider, 0.5)}`
                        : 'none',
                      transition: 'transform 0.2s ease',
                      '&:hover': {
                        transform: 'translateX(4px)',
                      }
                    }}
                  >
                    <Grid container spacing={{ xs: 1, sm: 2 }} alignItems="center">
                      <Grid item xs={7} sm={6}>
                        <Typography
                          variant="subtitle2"
                          sx={{
                            fontSize: { xs: '0.85rem', sm: '0.95rem' },
                            fontWeight: 500,
                          }}
                        >
                          {plan.installment}
                        </Typography>
                      </Grid>
                      <Grid item xs={5} sm={6} sx={{ textAlign: 'right' }}>
                        <Typography
                          variant="subtitle1"
                          sx={{
                            fontWeight: 700,
                            fontSize: { xs: '1rem', sm: '1.1rem' },
                            color: theme.palette.primary.main,
                            background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent',
                          }}
                        >
                          {plan.percentage}%
                        </Typography>
                      </Grid>
                    </Grid>
                    {plan.description && (
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          mt: 1,
                          fontSize: { xs: '0.8rem', sm: '0.875rem' },
                          lineHeight: 1.5,
                          pl: { xs: 0, sm: 1 },
                          borderLeft: { xs: 'none', sm: `2px solid ${alpha(theme.palette.primary.main, 0.2)}` },
                        }}
                      >
                        {plan.description}
                      </Typography>
                    )}
                  </Box>
                ))}
              </Box>
            ) : (
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  py: 4,
                  gap: 2,
                }}
              >
                <Box
                  component="img"
                  src="https://cdn-icons-png.flaticon.com/512/4076/4076478.png"
                  alt="No payment plan"
                  sx={{
                    width: { xs: 50, sm: 60 },
                    height: { xs: 50, sm: 60 },
                    opacity: 0.5
                  }}
                />
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ textAlign: 'center' }}
                >
                  No payment plan details available
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>

        {/* Right Column - Details */}
        <Grid item xs={12} md={5}>
          <Paper
            elevation={2}
            sx={{
              p: 3,
              borderRadius: 3,
              mb: 3
            }}
          >
            {/* Title and Admin Actions */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
              <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
                {property.title}
              </Typography>

              <Box display="flex" alignItems="center">
                {/* Share Button */}
                <ShareButton
                  propertyId={property._id}
                  propertyTitle={property.title}
                  propertyImage={mainImage}
                  propertyLocation={property.location}
                  propertyPrice={property.formattedPrice}
                  shareUrl={shareUrl}
                  apiUrl={apiUrl}
                  variant="icon"
                  size="medium"
                />

                {/* Admin Actions */}
                {isSuperUser && (
                  <>
                    <IconButton
                      color="primary"
                      onClick={() => navigate(`/exclusive/edit/${property._id}`)}
                      sx={{ ml: 1 }}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      color="error"
                      onClick={() => setDeleteConfirm(true)}
                      sx={{ ml: 1 }}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </>
                )}
              </Box>
            </Box>

            {/* Location */}
            <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 3 }}>
              <LocationOn color="action" />
              <Typography variant="h6" color="text.secondary">
                {property.location}
              </Typography>
            </Stack>

            {/* Price */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Price Range
              </Typography>
              <Typography variant="h5" color="primary" fontWeight="bold">
                {property.formattedPrice}
              </Typography>
            </Box>

            <Divider sx={{ my: 3 }} />

            {/* Property Details */}
            <Grid container spacing={3} sx={{ mb: 3 }}>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Property Type
                </Typography>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Apartment fontSize="small" color="action" />
                  <Typography variant="body1">
                    {property.propertyType}
                  </Typography>
                </Stack>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Typology
                </Typography>
                <Typography variant="body1">
                  {property.typology}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Total Area
                </Typography>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <SquareFoot fontSize="small" color="action" />
                  <Typography variant="body1">
                    {property.totalArea} Sq.ft
                  </Typography>
                </Stack>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Possession Date
                </Typography>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <CalendarToday fontSize="small" color="action" />
                  <Typography variant="body1">
                    {property.formattedPossessionDate}
                  </Typography>
                </Stack>
              </Grid>
              <Grid item xs={6}>
                <Typography
                  variant="body2"
                  sx={{
                    color: theme.palette.secondary.main,
                    fontWeight: 600,
                    fontSize: '0.85rem',
                    textTransform: 'uppercase',
                    letterSpacing: '0.5px'
                  }}
                  gutterBottom
                >
                  Commission Range
                </Typography>
                <Box
                  sx={{
                    p: 1.2,
                    borderRadius: 1.5,
                    background: `linear-gradient(135deg, ${alpha(theme.palette.secondary.light, 0.15)} 0%, ${alpha(theme.palette.secondary.main, 0.25)} 100%)`,
                    border: `1px solid ${alpha(theme.palette.secondary.main, 0.3)}`,
                    boxShadow: `0 4px 12px ${alpha(theme.palette.secondary.main, 0.15)}`,
                    transform: 'scale(1.05)',
                    position: 'relative',
                    overflow: 'hidden',
                    '&::after': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '100%',
                      background: `linear-gradient(45deg, transparent 0%, ${alpha(theme.palette.secondary.light, 0.1)} 50%, transparent 100%)`,
                      animation: 'shine 2s infinite',
                    },
                    '@keyframes shine': {
                      '0%': { transform: 'translateX(-100%)' },
                      '100%': { transform: 'translateX(100%)' }
                    }
                  }}
                >
                  <Stack direction="row" alignItems="center" justifyContent="center" spacing={1}>
                    <CurrencyRupee sx={{ color: theme.palette.secondary.main, fontSize: '1.2rem' }} />
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 700,
                        color: theme.palette.secondary.main,
                        textShadow: `0 1px 2px ${alpha(theme.palette.secondary.main, 0.2)}`,
                        fontSize: { xs: '1.1rem', sm: '1.3rem' }
                      }}
                    >
                      {property.formattedCommission}
                    </Typography>
                  </Stack>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  RERA Number
                </Typography>
                <Typography variant="body1">
                  {property.reraNumber}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Total Bookings
                </Typography>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Typography variant="body1">
                    {property.totalBookings || 0}
                  </Typography>
                </Stack>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Configuration
                </Typography>
                <Typography variant="body1">
                  {property.configuration} Sq.ft
                </Typography>
              </Grid>
            </Grid>

            <Divider sx={{ my: 3 }} />

            {/* External Links */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                External Resources
              </Typography>
              <Stack spacing={2}>
                {property.websiteLink && (
                  <Button
                    variant="outlined"
                    startIcon={<OpenInNew />}
                    href={property.websiteLink}
                    target="_blank"
                    rel="noopener noreferrer"
                    fullWidth
                  >
                    Visit Website
                  </Button>
                )}
                {property.brochureLink && (
                  <Button
                    variant="outlined"
                    startIcon={<Description />}
                    href={property.brochureLink}
                    target="_blank"
                    rel="noopener noreferrer"
                    fullWidth
                  >
                    View Brochure
                  </Button>
                )}
                {property.gpsLocation && (
                  <Button
                    variant="outlined"
                    startIcon={<LocationOn />}
                    href={property.gpsLocation}
                    target="_blank"
                    rel="noopener noreferrer"
                    fullWidth
                  >
                    View on Google Maps
                  </Button>
                )}
                {(!property.websiteLink && !property.brochureLink && !property.gpsLocation) && (
                  <Typography variant="body2" color="text.secondary">
                    No external resources available
                  </Typography>
                )}
              </Stack>
            </Box>

            {/* Floor Plans */}
            {property.floorPlanLinks && property.floorPlanLinks.length > 0 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Floor Plans
                </Typography>
                <Stack spacing={2}>
                  {property.floorPlanLinks.map((link, index) => (
                    <Button
                      key={index}
                      variant="outlined"
                      startIcon={<OpenInNew />}
                      href={link}
                      target="_blank"
                      rel="noopener noreferrer"
                      fullWidth
                    >
                      Floor Plan {index + 1}
                    </Button>
                  ))}
                </Stack>
              </Box>
            )}

            {/* Views Counter - Enhanced */}
            <Box
              component={motion.div}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              sx={{
                mt: 4,
                mb: 4,
                p: { xs: 2, sm: 2.5 },
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: `linear-gradient(135deg, ${alpha(theme.palette.primary.light, 0.05)} 0%, ${alpha(theme.palette.primary.main, 0.1)} 100%)`,
                borderRadius: { xs: 2, sm: 2.5 },
                border: `1px solid ${alpha(theme.palette.primary.main, 0.12)}`,
                boxShadow: `0 4px 20px ${alpha(theme.palette.primary.main, 0.05)}`,
                position: 'relative',
                overflow: 'hidden'
              }}
            >
              {/* Background Pattern */}
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  opacity: 0.03,
                  backgroundImage: 'radial-gradient(circle, rgba(0,0,0,0.2) 1px, transparent 1px)',
                  backgroundSize: '10px 10px',
                  zIndex: 0,
                }}
              />

              <Stack
                direction="row"
                alignItems="center"
                spacing={1.5}
                sx={{ position: 'relative', zIndex: 1 }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    bgcolor: alpha(theme.palette.primary.main, 0.15),
                    borderRadius: '50%',
                    p: 1,
                    width: 36,
                    height: 36,
                  }}
                >
                  <VisibilityIcon
                    sx={{
                      color: theme.palette.primary.main,
                      fontSize: 20
                    }}
                  />
                </Box>
                <Box>
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 700,
                      fontSize: { xs: '1.1rem', sm: '1.2rem' },
                      color: theme.palette.primary.main,
                      lineHeight: 1.2,
                    }}
                  >
                    {viewCount}
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      fontWeight: 500,
                      color: theme.palette.text.secondary,
                      fontSize: { xs: '0.75rem', sm: '0.8rem' },
                    }}
                  >
                    people viewed this property
                  </Typography>
                </Box>
              </Stack>
            </Box>

            {/* Contact BrickBix - Enhanced */}
            <Box
              component={motion.div}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              sx={{
                mt: 4,
                p: { xs: 2.5, sm: 3 },
                bgcolor: alpha(theme.palette.primary.main, 0.05),
                borderRadius: { xs: 2, sm: 3 },
                border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                boxShadow: `0 10px 30px ${alpha(theme.palette.primary.main, 0.07)}`,
                position: 'relative',
                overflow: 'hidden',
              }}
            >
              {/* Background Pattern */}
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  right: 0,
                  width: '150px',
                  height: '150px',
                  opacity: 0.05,
                  backgroundImage: 'radial-gradient(circle, rgba(0,0,0,0.2) 2px, transparent 2px)',
                  backgroundSize: '15px 15px',
                  transform: 'rotate(15deg)',
                  zIndex: 0,
                }}
              />

              <Box sx={{ position: 'relative', zIndex: 1 }}>
                <Typography
                  variant="h6"
                  sx={{
                    fontSize: { xs: '1.1rem', sm: '1.2rem' },
                    fontWeight: 700,
                    mb: 2,
                    color: theme.palette.primary.dark,
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                  }}
                >
                  <Info fontSize="small" /> Contact BrickBix
                </Typography>

                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: { xs: 'column', sm: 'row' },
                    alignItems: { xs: 'flex-start', sm: 'center' },
                    gap: { xs: 1, sm: 3 },
                    mb: 3,
                    pb: 2,
                    borderBottom: `1px dashed ${alpha(theme.palette.divider, 0.5)}`,
                  }}
                >
                  <Typography
                    variant="body1"
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                      fontWeight: 600,
                      color: theme.palette.text.primary,
                    }}
                  >
                    <Box
                      component="span"
                      sx={{
                        color: theme.palette.primary.main,
                        display: 'inline-flex',
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                        p: 0.8,
                        borderRadius: '50%',
                      }}
                    >
                      <Phone fontSize="small" />
                    </Box>
                    +91 93401 99672
                  </Typography>

                  <Chip
                    label="Exclusive Property Expert"
                    size="small"
                    color="primary"
                    variant="outlined"
                    sx={{
                      fontWeight: 500,
                      borderRadius: '4px',
                      height: '24px',
                    }}
                  />
                </Box>

                <Typography
                  variant="body2"
                  sx={{
                    mb: 3,
                    color: alpha(theme.palette.text.secondary, 0.9),
                    fontStyle: 'italic',
                    pl: 1,
                    borderLeft: `3px solid ${alpha(theme.palette.primary.main, 0.3)}`,
                  }}
                >
                  Contact our experts for more details about this exclusive property and special offers for real estate professionals.
                </Typography>

                <Stack
                  direction={{ xs: 'column', sm: 'row' }}
                  spacing={{ xs: 1.5, sm: 2 }}
                >
                  <Button
                    component={motion.button}
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.98 }}
                    variant="contained"
                    color="primary"
                    fullWidth
                    startIcon={<Call />}
                    onClick={() => window.location.href = `tel:+919340199672`}
                    sx={{
                      py: { xs: 1.2, sm: 1.5 },
                      borderRadius: 2,
                      textTransform: 'none',
                      fontWeight: 600,
                      boxShadow: '0 4px 10px rgba(0,0,0,0.1)',
                      '&:hover': {
                        boxShadow: '0 6px 15px rgba(0,0,0,0.15)',
                      }
                    }}
                  >
                    Call Now
                  </Button>
                  <Button
                    component={motion.button}
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.98 }}
                    variant="outlined"
                    color="success"
                    fullWidth
                    startIcon={<WhatsApp />}
                    onClick={() => window.open(`https://wa.me/919340199672?text=I'm interested in the exclusive property: ${property.title} (${shareUrl})`, '_blank')}
                    sx={{
                      py: { xs: 1.2, sm: 1.5 },
                      borderRadius: 2,
                      textTransform: 'none',
                      fontWeight: 600,
                      borderColor: '#25D366',
                      color: '#25D366',
                      borderWidth: 2,
                      '&:hover': {
                        borderColor: '#25D366',
                        backgroundColor: alpha('#25D366', 0.05),
                        borderWidth: 2,
                      }
                    }}
                  >
                    WhatsApp
                  </Button>
                </Stack>
              </Box>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Enhanced Image Modal */}
      <Modal
        open={isImageModalOpen}
        onClose={() => setIsImageModalOpen(false)}
        closeAfterTransition
        slots={{
          backdrop: Backdrop
        }}
        slotProps={{
          backdrop: {
            timeout: 500,
            sx: { backdropFilter: 'blur(5px)', backgroundColor: alpha(theme.palette.common.black, 0.85) }
          },
        }}
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          p: { xs: 1, sm: 2, md: 3 },
        }}
      >
        <Fade in={isImageModalOpen}>
          <Box
            component={motion.div}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
            sx={{
              position: 'relative',
              maxWidth: '95vw',
              maxHeight: '95vh',
              bgcolor: 'transparent',
              borderRadius: 3,
              boxShadow: `0 25px 50px -12px ${alpha(theme.palette.common.black, 0.7)}`,
              outline: 'none',
              overflow: 'hidden',
            }}
          >
            {/* Close button */}
            <IconButton
              size="medium"
              onClick={() => setIsImageModalOpen(false)}
              sx={{
                position: 'absolute',
                right: { xs: 8, sm: 16 },
                top: { xs: 8, sm: 16 },
                bgcolor: alpha(theme.palette.common.black, 0.5),
                color: theme.palette.common.white,
                zIndex: 10,
                '&:hover': {
                  bgcolor: alpha(theme.palette.common.black, 0.7),
                  transform: 'scale(1.1)',
                },
                transition: 'all 0.2s ease',
                width: { xs: 36, sm: 42 },
                height: { xs: 36, sm: 42 },
              }}
            >
              <Typography variant="h6">×</Typography>
            </IconButton>

            {/* Image with Animation */}
            <Box
              component={motion.img}
              key={modalImageIndex} // Key for animation when image changes
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
              src={property.images[modalImageIndex]}
              alt={`${property.title} - Image ${modalImageIndex + 1}`}
              sx={{
                width: '100%',
                height: 'auto',
                maxHeight: '95vh',
                objectFit: 'contain',
                display: 'block',
                backgroundColor: 'transparent',
              }}
            />

            {/* Enhanced Navigation buttons */}
            {property.images.length > 1 && (
              <>
                <IconButton
                  component={motion.button}
                  whileHover={{ scale: 1.1, x: -5 }}
                  whileTap={{ scale: 0.9 }}
                  size="large"
                  sx={{
                    position: 'absolute',
                    left: { xs: 10, sm: 20 },
                    top: '50%',
                    transform: 'translateY(-50%)',
                    bgcolor: alpha(theme.palette.common.black, 0.5),
                    color: theme.palette.common.white,
                    width: { xs: 40, sm: 50 },
                    height: { xs: 40, sm: 50 },
                    '&:hover': {
                      bgcolor: alpha(theme.palette.common.black, 0.7),
                    },
                  }}
                  onClick={() => {
                    setModalImageIndex(prev =>
                      prev === 0 ? property.images.length - 1 : prev - 1
                    );
                  }}
                >
                  <ArrowBackIos sx={{ fontSize: { xs: '1.2rem', sm: '1.5rem' }, ml: 1 }} />
                </IconButton>

                <IconButton
                  component={motion.button}
                  whileHover={{ scale: 1.1, x: 5 }}
                  whileTap={{ scale: 0.9 }}
                  size="large"
                  sx={{
                    position: 'absolute',
                    right: { xs: 10, sm: 20 },
                    top: '50%',
                    transform: 'translateY(-50%)',
                    bgcolor: alpha(theme.palette.common.black, 0.5),
                    color: theme.palette.common.white,
                    width: { xs: 40, sm: 50 },
                    height: { xs: 40, sm: 50 },
                    '&:hover': {
                      bgcolor: alpha(theme.palette.common.black, 0.7),
                    },
                  }}
                  onClick={() => {
                    setModalImageIndex(prev =>
                      prev === property.images.length - 1 ? 0 : prev + 1
                    );
                  }}
                >
                  <ArrowForwardIos sx={{ fontSize: { xs: '1.2rem', sm: '1.5rem' } }} />
                </IconButton>

                {/* Enhanced Image counter */}
                <Box
                  component={motion.div}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  sx={{
                    position: 'absolute',
                    bottom: { xs: 16, sm: 24 },
                    left: '50%',
                    transform: 'translateX(-50%)',
                    bgcolor: alpha(theme.palette.common.black, 0.6),
                    color: theme.palette.common.white,
                    px: { xs: 2, sm: 3 },
                    py: { xs: 0.5, sm: 0.8 },
                    borderRadius: 30,
                    fontSize: { xs: '0.85rem', sm: '1rem' },
                    fontWeight: 600,
                    boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                  }}
                >
                  <VisibilityIcon sx={{ fontSize: { xs: 16, sm: 18 } }} />
                  {modalImageIndex + 1} / {property.images.length}
                </Box>
              </>
            )}
          </Box>
        </Fade>
      </Modal>
    </Container>
  );
};

export default ExclusivePropertyDetails;
