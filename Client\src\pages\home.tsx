import React, { useEffect, useState, useMemo, useRef } from "react";
import { useGetIdentity, useActiveAuthProvider } from "@refinedev/core";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import Stack from "@mui/material/Stack";
import Skeleton from "@mui/material/Skeleton";
import Alert from "@mui/material/Alert";
import BrickBixImage from '../assets/brick bix image.jpg';
import PieChart from '../components/charts/PieChart';
import PropertyCard from "../components/common/PropertyCard";
import CustomButton from "../components/common/CustomButton";
import { useNavigate } from "react-router-dom";
import { Add, Home as HomeIcon, Assignment, Star, ArrowForward, TrendingUp } from "@mui/icons-material";
import Service from "../components/common/Services";
import UserForm from "../components/common/UserForm";
import Dialog from "@mui/material/Dialog";
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { alpha } from '@mui/material/styles';
import Container from '@mui/material/Container';
import Paper from '@mui/material/Paper';
import Fade from '@mui/material/Fade';
import Grid from '@mui/material/Grid';
import { motion } from 'framer-motion';
import ExclusivePropertyCard from "../components/common/ExclusivePropertyCard";

interface Property {
    _id: string;
    title: string;
    location: string;
    dealType: string;
    price: number;
    photo: string;
    phone: string;
    propertyType: string;
}

interface Requirement extends Omit<Property, 'price' | 'photo'> {
    askedPrice: number;
}

interface UserInfo {
    name: string;
    phoneNumber: string;
    workLocation: string;
}

interface DashboardStats {
    totalPropertiesCount: number;
    commercialPropertiesCount: number;
    apartmentPropertiesCount: number;
    totalRequirementsCount: number;
}

interface ExclusiveProperty {
    _id: string;
    title: string;
    description: string;
    location: string;
    propertyType: string;
    priceRange: {
        min: number;
        max: number;
    };
    typology: string;
    commissionRange: {
        min: number;
        max: number;
    };
    configuration: string;
    totalArea: number;
    gpsLocation?: string;
    images: string[];
    creator: {
        _id: string;
        name: string;
        email: string;
        avatar?: string;
    };
    createdAt: string;
    viewCount?: number;
    formattedPrice?: string;
    formattedCommission?: string;
}

const INITIAL_STATS: DashboardStats = {
    totalPropertiesCount: 0,
    commercialPropertiesCount: 0,
    apartmentPropertiesCount: 0,
    totalRequirementsCount: 0,
};

const Home = () => {
    const navigate = useNavigate();
    const authProvider = useActiveAuthProvider();
    const { data: user } = useGetIdentity({
        v3LegacyAuthProviderCompatible: Boolean(authProvider?.isLegacy),
    });
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

    // State management
    const [properties, setProperties] = useState<Property[]>([]);
    const [requirements, setRequirements] = useState<Requirement[]>([]);
    const [exclusiveProperties, setExclusiveProperties] = useState<ExclusiveProperty[]>([]);
    const [stats, setStats] = useState<DashboardStats>(INITIAL_STATS);
    const [loading, setLoading] = useState(true);
    const [exclusiveLoading, setExclusiveLoading] = useState(true);
    const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
    const [dialogState, setDialogState] = useState({
        showDialog: false,
        formSubmitted: false,
        showThankYou: false,
        error: '',
    });
    const [dataLoaded, setDataLoaded] = useState(false); // New state to track data loading
    const [isPropertiesCarouselEndReached, setIsPropertiesCarouselEndReached] = useState(false);
    const [isRequirementsCarouselEndReached, setIsRequirementsCarouselEndReached] = useState(false);
    const [isExclusivePropertiesCarouselEndReached, setIsExclusivePropertiesCarouselEndReached] = useState(false);


    const apiUrl = import.meta.env.VITE_API_URL;
    const userId = user?.id || user?.userid;

    // Refs for carousels
    const propertiesCarouselRef = useRef<HTMLDivElement>(null);
    const requirementsCarouselRef = useRef<HTMLDivElement>(null);
    const exclusivePropertiesCarouselRef = useRef<HTMLDivElement>(null);

    // Check if user info is complete
    const isUserInfoComplete = userInfo &&
        userInfo.phoneNumber &&
        userInfo.phoneNumber.trim() !== '' &&
        userInfo.workLocation &&
        userInfo.workLocation.trim() !== '';

    // Fetch data from API
    const fetchDashboardData = async () => {
        if (!userId) return;

        try {
            setLoading(true);
            const [propertiesRes, requirementsRes, userInfoRes] = await Promise.all([
                fetch(`${apiUrl}/api/v1/properties/five`),
                fetch(`${apiUrl}/api/v1/requirement/five`),
                fetch(`${apiUrl}/api/v1/users/${userId}`)
            ]);

            // Check if any of the responses failed
            if (!propertiesRes.ok || !requirementsRes.ok || !userInfoRes.ok) {
                throw new Error("Failed to fetch data");
            }

            const [userInfoData, propertiesData, requirementsData] = await Promise.all([
                userInfoRes.json(),
                propertiesRes.json(),
                requirementsRes.json(),
            ]);

            // Fallback values if data is incomplete
            setProperties(propertiesData?.properties || []);
            setRequirements(requirementsData?.requirements || []);
            setUserInfo(userInfoData || null);
            setStats({
                totalPropertiesCount: propertiesData?.totalPropertiesCount || 0,
                commercialPropertiesCount: propertiesData?.commercialPropertiesCount || 0,
                apartmentPropertiesCount: propertiesData?.apartmentPropertiesCount || 0,
                totalRequirementsCount: requirementsData?.totalRequirementsCount || 0,
            });
            setDataLoaded(true); // Set dataLoaded to true after successful fetch

            // Only show dialog if user info is not complete
            setDialogState(prev => ({
                ...prev,
                showDialog: !isUserInfoComplete,
                error: ''
            }));

        } catch (error) {
            console.error("Error fetching dashboard data:", error);

            // Set error state
            setDialogState(prev => ({
                ...prev,
                showDialog: false,
                error: "Unable to load dashboard data. Please try again later."
            }));

            // Set fallback states
            setProperties([]);
            setRequirements([]);
            setStats(INITIAL_STATS);
        } finally {
            setLoading(false);
        }
    };

    // Fetch exclusive properties
    const fetchExclusiveProperties = async () => {
        try {
            setExclusiveLoading(true);
            const response = await fetch(`${apiUrl}/api/v1/exclusive-properties`);

            if (!response.ok) {
                throw new Error("Failed to fetch exclusive properties");
            }

            const data = await response.json();

            // Format the price and commission for display
            const formattedProperties = data.map((property: ExclusiveProperty) => ({
                ...property,
                formattedPrice: `₹${property.priceRange.min.toLocaleString('en-IN')} - ₹${property.priceRange.max.toLocaleString('en-IN')}`,
                formattedCommission: property.commissionRange ?
                    `${property.commissionRange.min}% - ${property.commissionRange.max}%` :
                    'Commission not specified',
                viewCount: property.viewCount || 0
            }));

            setExclusiveProperties(formattedProperties);
        } catch (error) {
            console.error("Error fetching exclusive properties:", error);
            setExclusiveProperties([]);
        } finally {
            setExclusiveLoading(false);
        }
    };

    useEffect(() => {
        document.title = "BrickBix";
        if (!dataLoaded) { // Check if data is already loaded
            fetchDashboardData();
            fetchExclusiveProperties();
        }
    }, [userId, dataLoaded]); // Added dataLoaded as dependency to useEffect, but it should not cause infinite loop as dataLoaded is set to true after first fetch

    // Memoized user name display
    const userNameDisplay = useMemo(() => (
        user?.name ? (
            <Typography
                sx={{ fontWeight: "bold", fontSize: '12px' }}
                variant="subtitle2"
                data-testid="header-user-name"
            >
                <span style={{ color: '#d84030' }}>Welcome</span>{' '}
                <span style={{ color: '#11418a' }}>{user.name.toUpperCase()}</span>!
            </Typography>
        ) : null
    ), [user?.name]);

    // Memoized pie chart data
    const pieCharts = useMemo(() => [
        {
            title: "Total Properties",
            value: stats.totalPropertiesCount,
            series: [stats.totalPropertiesCount, 1000 - stats.totalPropertiesCount],
        },
        {
            title: "Commercial Properties",
            value: stats.commercialPropertiesCount,
            series: [stats.commercialPropertiesCount, 100 - stats.commercialPropertiesCount],
        },
        {
            title: "Apartments",
            value: stats.apartmentPropertiesCount,
            series: [stats.apartmentPropertiesCount, 500 - stats.apartmentPropertiesCount],
        },
        {
            title: "Requirements Listed",
            value: stats.totalRequirementsCount,
            series: [stats.totalRequirementsCount, 1000 - stats.totalRequirementsCount],
        }
    ], [stats]);

    // Form submission handler
    const handleFormSubmit = (success: boolean) => {
        if (success) {
            setDialogState({
                formSubmitted: true,
                showThankYou: true,
                showDialog: false,
                error: '',
            });

            setTimeout(() => {
                setDialogState(prev => ({ ...prev, showThankYou: false }));
                fetchDashboardData(); // Refresh data after form submission
            }, 3000);
        }
    };

    // Render property cards
    const renderPropertyCards = (items: Property[] | Requirement[], type: 'properties' | 'requirements', carouselRef: React.RefObject<HTMLDivElement>, isCarouselEndReached: boolean, setIsCarouselEndReached: React.Dispatch<React.SetStateAction<boolean>>) => {
        if (loading) {
            return Array.from(new Array(3)).map((_, index) => (
                <Box key={index} sx={{ width: isMobile ? '80vw' : 250, height: 200, padding: 0 }}>
                    <Skeleton variant="rectangular" width="100%" height="50%" sx={{ borderRadius: '20px' }} />
                    <Skeleton variant="text" width="60%" sx={{ marginTop: 1, borderRadius: '20px' }} />
                    <Skeleton variant="text" width="40%" sx={{ borderRadius: '20px' }} />
                    <Skeleton variant="text" width="30%" sx={{ marginTop: 1, borderRadius: '20px' }} />
                </Box>
            ));
        }

        return (
            <>
                {items
                    .slice()
                    .reverse()
                    .slice(0, 5)
                    .map((item) => (
                        <Box key={item._id} sx={{
                            minWidth: isMobile ? '80vw' : '300px',
                            maxWidth: isMobile ? '80vw' : '400px',
                            width: isMobile ? '80vw' : '350px',
                            scrollSnapAlign: 'start'
                        }}>
                            <PropertyCard
                                id={item._id}
                                title={item.title}
                                location={item.location}
                                dealType={item.dealType}
                                //@ts-ignore
                                price={type === 'properties' ? (item as Property).price : (item as Requirement).askedPrice}
                                phone={item.phone}
                                //@ts-ignore
                                photo={type === 'properties' ? (item as Property).photo || (item as Property).photos: BrickBixImage}
                                propertyType={item.propertyType}
                                url={type === 'properties' ? "properties" : "properties-requirement"}
                            />
                        </Box>
                    ))}
                 <Box sx={{
                        minWidth: isMobile ? '80vw' : '300px',
                        maxWidth: isMobile ? '80vw' : '400px',
                        width: isMobile ? '80vw' : '350px',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        scrollSnapAlign: 'start', // Ensure button also snaps into view
                        p: 2
                    }}>
                        <Box sx={{ width: '100%', minWidth: '200px' }}>
                            <CustomButton
                                title={`View All ${type === 'properties' ? 'Properties' : 'Requirements'}`}
                                handleClick={() => {
                                    if (type === 'properties') {
                                        navigate("/allProperties");
                                    } else if (type === 'requirements') {
                                        navigate("/requirement");
                                    }
                                }}
                                backgroundColor="#0F52BA"
                                color="#fcfcfc"
                                fullWidth
                                icon={<ArrowForward />}
                            />
                        </Box>
                    </Box>
            </>
        );
    };

    // Render exclusive property cards
    const renderExclusivePropertyCards = () => {
        if (exclusiveLoading) {
            return Array.from(new Array(3)).map((_, index) => (
                <Box key={index} sx={{
                    width: isMobile ? '80vw' : 300,
                    height: 300,
                    padding: 0,
                    scrollSnapAlign: 'start'
                }}>
                    <Skeleton variant="rectangular" width="100%" height="60%" sx={{ borderRadius: '8px 8px 0 0' }} />
                    <Box sx={{ p: 1.5 }}>
                        <Skeleton variant="text" width="70%" sx={{ marginTop: 1 }} />
                        <Skeleton variant="text" width="50%" />
                        <Skeleton variant="text" width="40%" sx={{ marginTop: 1 }} />
                        <Skeleton variant="text" width="30%" sx={{ marginTop: 1 }} />
                        <Skeleton variant="rectangular" width="100%" height={36} sx={{ mt: 1, borderRadius: '4px' }} />
                    </Box>
                </Box>
            ));
        }

        if (exclusiveProperties.length === 0) {
            return (
                <Box sx={{
                    width: '100%',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    py: 4
                }}>
                    <Typography variant="body1" color="text.secondary">
                        No exclusive properties available at the moment.
                    </Typography>
                </Box>
            );
        }

        return (
            <>
                {exclusiveProperties
                    .slice(0, 3)
                    .map((property, index) => (
                        <Box
                            key={property._id}
                            sx={{
                                minWidth: isMobile ? '80vw' : '300px',
                                maxWidth: isMobile ? '80vw' : '400px',
                                width: isMobile ? '80vw' : '350px',
                                scrollSnapAlign: 'start',
                                height: '100%'
                            }}
                        >
                            <ExclusivePropertyCard
                                property={property}
                                index={index}
                            />
                        </Box>
                    ))}

                <Box sx={{
                    minWidth: isMobile ? '80vw' : '300px',
                    maxWidth: isMobile ? '80vw' : '400px',
                    width: isMobile ? '80vw' : '350px',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    scrollSnapAlign: 'start',
                    p: 2
                }}>
                    <Box sx={{ width: '100%', minWidth: '200px' }}>
                        <CustomButton
                            title="View All Exclusive Properties"
                            handleClick={() => navigate("/exclusive")}
                            backgroundColor="#0F52BA"
                            color="#fcfcfc"
                            icon={<ArrowForward />}
                            fullWidth
                        />
                    </Box>
                </Box>
            </>
        );
    };

    const handlePropertiesCarouselScroll = () => {
        if (propertiesCarouselRef.current) {
            const { scrollLeft, scrollWidth, clientWidth } = propertiesCarouselRef.current;
            const isEnd = Math.abs(scrollWidth - clientWidth - scrollLeft) < 1;
            setIsPropertiesCarouselEndReached(isEnd);
        }
    };

    const handleRequirementsCarouselScroll = () => {
        if (requirementsCarouselRef.current) {
            const { scrollLeft, scrollWidth, clientWidth } = requirementsCarouselRef.current;
            const isEnd = Math.abs(scrollWidth - clientWidth - scrollLeft) < 1;
            setIsRequirementsCarouselEndReached(isEnd);
        }
    };

    const handleExclusivePropertiesCarouselScroll = () => {
        if (exclusivePropertiesCarouselRef.current) {
            const { scrollLeft, scrollWidth, clientWidth } = exclusivePropertiesCarouselRef.current;
            const isEnd = Math.abs(scrollWidth - clientWidth - scrollLeft) < 1;
            setIsExclusivePropertiesCarouselEndReached(isEnd);
        }
    };


    return (
        <Container
            maxWidth="xl"
            sx={{
                py: { xs: 1, sm: 1.5, md: 2 },
                px: { xs: 1, sm: 1.5, md: 2 }
            }}
        >
            {/* Welcome Section */}
            <Fade in timeout={500}>
                <Paper
                    elevation={0}
                    sx={{
                        p: { xs: 1.5, sm: 2, md: 2.5 },
                        mb: { xs: 2, sm: 2.5, md: 3 },
                        background: 'linear-gradient(135deg, #f0f4ff 0%, #f5f7fa 100%)',
                        borderRadius: { xs: 1.5, sm: 2 },
                        position: 'relative',
                        overflow: 'hidden',
                        boxShadow: '0 2px 10px rgba(0,0,0,0.03)',
                    }}
                >
                    <Stack
                        direction={{ xs: 'column', sm: 'row' }}
                        spacing={{ xs: 2, sm: 2.5 }}
                        alignItems={{ xs: 'stretch', sm: 'center' }}
                        justifyContent="space-between"
                    >
                        <Box>
                            <Typography
                                variant="h5"
                                sx={{
                                    fontWeight: 700,
                                    color: 'primary.main',
                                    mb: 0.5,
                                    fontSize: {
                                        xs: '1.25rem',
                                        sm: '1.4rem',
                                        md: '1.6rem',
                                    },
                                }}
                            >
                                Welcome to <span style={{ color: '#d84030' }}>Brick</span><span style={{ color: '#11418a' }}>Bix</span>
                            </Typography>
                            <Box>
                                {userNameDisplay}
                            </Box>
                        </Box>

                        <Stack
                            direction={{ xs: 'row', sm: 'row' }}
                            spacing={{ xs: 1.5, sm: 2 }}
                            sx={{
                                width: '100%',
                                flexWrap: { xs: 'nowrap', sm: 'nowrap' }
                            }}
                        >
                            <Box sx={{ width: '50%', minWidth: { xs: '150px', sm: '180px', md: '200px' } }}>
                                <CustomButton
                                    title="Add Property"
                                    handleClick={() => navigate("/allProperties/properties/create")}
                                    backgroundColor="#0F52BA"
                                    color="#fcfcfc"
                                    icon={<Add />}
                                    fullWidth
                                />
                            </Box>
                            <Box sx={{ width: '50%', minWidth: { xs: '150px', sm: '180px', md: '200px' } }}>
                                <CustomButton
                                    title="Add Requirement"
                                    handleClick={() => navigate("requirement/properties-requirement/create")}
                                    backgroundColor="#0F52BA"
                                    color="#fcfcfc"
                                    icon={<Add />}
                                    fullWidth
                                />
                            </Box>
                        </Stack>
                    </Stack>
                </Paper>
            </Fade>

            {/* Error Alert */}
            {dialogState.error && (
                <Fade in timeout={500}>
                    <Alert
                        severity="error"
                        sx={{
                            mb: { xs: 2, sm: 3 },
                            borderRadius: { xs: 1, sm: 2 },
                            boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                        }}
                        onClose={() => setDialogState(prev => ({ ...prev, error: '' }))}
                    >
                        {dialogState.error}
                    </Alert>
                </Fade>
            )}

            {/* User Form Dialog */}
            {!isUserInfoComplete && !dialogState.formSubmitted && (
                <Dialog
                    open={dialogState.showDialog}
                    PaperProps={{
                        style: {
                            borderRadius: '20px',
                            maxWidth: '500px',
                            width: '100%',
                            margin: '16px'
                        }
                    }}
                >
                    <UserForm
                        name={user?.name}
                        email={user?.email}
                        onFormSubmit={handleFormSubmit}
                    />
                </Dialog>
            )}

            {/* Thank You Message */}
            {dialogState.showThankYou && (
                <Fade in timeout={500}>
                    <Paper
                        elevation={0}
                        sx={{
                            p: { xs: 2, sm: 3 },
                            mb: { xs: 2, sm: 3, md: 4 },
                            textAlign: 'center',
                            backgroundColor: '#e8f5e9',
                            borderRadius: { xs: 1, sm: 2 },
                            boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                        }}
                    >
                        <Typography
                            variant="h6"
                            color="success.main"
                            sx={{
                                fontWeight: 600,
                                fontSize: { xs: '1rem', sm: '1.25rem' }
                            }}
                        >
                            Thank you for submitting your information!
                        </Typography>
                    </Paper>
                </Fade>
            )}

            {/* Latest Properties Section */}
            <Fade in timeout={800}>
                <Paper
                    elevation={0}
                    sx={{
                        p: { xs: 1.5, sm: 2 },
                        mb: { xs: 1.5, sm: 2 },
                        borderRadius: { xs: 1, sm: 1.5 },
                        background: '#F6F5F2',
                    }}
                >
                    <Stack
                        direction="row"
                        alignItems="center"
                        spacing={1.5}
                        mb={{ xs: 1, sm: 1.5 }}
                    >
                        <HomeIcon
                            sx={{
                                color: theme.palette.primary.main,
                                fontSize: { xs: 22, sm: 24 }
                            }}
                        />
                        <Typography
                            variant="h6"
                            sx={{
                                fontWeight: 600,
                                color: '#11142d',
                                fontSize: {
                                    xs: '1rem',
                                    sm: '1.1rem',
                                    md: '1.2rem'
                                }
                            }}
                        >
                            Latest Properties
                        </Typography>
                    </Stack>
                    <Box
                        ref={propertiesCarouselRef}
                        sx={{
                            display: "flex",
                            gap: { xs: '8px', sm: '10px' },
                            overflowX: 'auto',
                            borderRadius: '8px',
                            scrollSnapType: 'x mandatory',
                            paddingY: { xs: '4px', sm: '6px' },
                            width: '100%',
                            scrollbarWidth: 'thin',
                            '&::-webkit-scrollbar': {
                                height: '4px'
                            },
                            '&::-webkit-scrollbar-thumb': {
                                backgroundColor: alpha('#000', 0.2),
                                borderRadius: '4px',
                                '&:hover': {
                                    backgroundColor: alpha('#000', 0.3),
                                }
                            },
                            '&::-webkit-scrollbar-track': {
                                backgroundColor: alpha('#000', 0.05),
                                borderRadius: '4px'
                            },
                        }}
                        onScroll={handlePropertiesCarouselScroll}
                    >
                        {renderPropertyCards(properties, 'properties', propertiesCarouselRef, isPropertiesCarouselEndReached, setIsPropertiesCarouselEndReached)}
                    </Box>
                </Paper>
            </Fade>

            {/* Exclusive Properties Section */}
            <Fade in timeout={800} style={{ transitionDelay: '150ms' }}>
                <Paper
                    elevation={0}
                    sx={{
                        p: { xs: 1.5, sm: 2 },
                        mb: { xs: 1.5, sm: 2 },
                        borderRadius: { xs: 1, sm: 1.5 },
                        background: 'linear-gradient(135deg, #f0f4ff 0%, #f5f7fa 100%)',
                    }}
                >
                    <Stack
                        direction="row"
                        alignItems="center"
                        spacing={1.5}
                        mb={{ xs: 1, sm: 1.5 }}
                    >
                        <Star
                            sx={{
                                color: '#0F52BA',
                                fontSize: { xs: 22, sm: 24 }
                            }}
                        />
                        <Typography
                            variant="h6"
                            sx={{
                                fontWeight: 600,
                                color: '#11142d',
                                fontSize: {
                                    xs: '1rem',
                                    sm: '1.1rem',
                                    md: '1.2rem'
                                }
                            }}
                        >
                            Exclusive Properties
                        </Typography>
                    </Stack>

                    <Box
                        ref={exclusivePropertiesCarouselRef}
                        sx={{
                            display: "flex",
                            gap: { xs: '8px', sm: '10px' },
                            overflowX: 'auto',
                            borderRadius: '8px',
                            scrollSnapType: 'x mandatory',
                            paddingY: { xs: '4px', sm: '6px' },
                            width: '100%',
                            scrollbarWidth: 'thin',
                            minHeight: { xs: '320px', sm: '350px' },
                            '&::-webkit-scrollbar': {
                                height: '4px'
                            },
                            '&::-webkit-scrollbar-thumb': {
                                backgroundColor: alpha('#000', 0.2),
                                borderRadius: '4px',
                                '&:hover': {
                                    backgroundColor: alpha('#000', 0.3),
                                }
                            },
                            '&::-webkit-scrollbar-track': {
                                backgroundColor: alpha('#000', 0.05),
                                borderRadius: '4px'
                            },
                        }}
                        onScroll={handleExclusivePropertiesCarouselScroll}
                    >
                        {renderExclusivePropertyCards()}
                    </Box>
                </Paper>
            </Fade>

            {/* Latest Requirements Section */}
            <Fade in timeout={800} style={{ transitionDelay: '300ms' }}>
                <Paper
                    elevation={0}
                    sx={{
                        p: { xs: 1.5, sm: 2 },
                        mb: { xs: 1.5, sm: 2 },
                        borderRadius: { xs: 1, sm: 1.5 },
                        background: '#F6F5F2',
                    }}
                >
                    <Stack
                        direction="row"
                        alignItems="center"
                        spacing={1.5}
                        mb={{ xs: 1, sm: 1.5 }}
                    >
                        <Assignment
                            sx={{
                                color: theme.palette.primary.main,
                                fontSize: { xs: 22, sm: 24 }
                            }}
                        />
                        <Typography
                            variant="h6"
                            sx={{
                                fontWeight: 600,
                                color: '#11142d',
                                fontSize: {
                                    xs: '1rem',
                                    sm: '1.1rem',
                                    md: '1.2rem'
                                }
                            }}
                        >
                            Latest Requirements
                        </Typography>
                    </Stack>
                    <Box
                        ref={requirementsCarouselRef}
                        sx={{
                            display: "flex",
                            gap: { xs: '8px', sm: '10px' },
                            overflowX: 'auto',
                            borderRadius: '8px',
                            scrollSnapType: 'x mandatory',
                            paddingY: { xs: '4px', sm: '6px' },
                            width: '100%',
                            scrollbarWidth: 'thin',
                            '&::-webkit-scrollbar': {
                                height: '4px'
                            },
                            '&::-webkit-scrollbar-thumb': {
                                backgroundColor: alpha('#000', 0.2),
                                borderRadius: '4px',
                                '&:hover': {
                                    backgroundColor: alpha('#000', 0.3),
                                }
                            },
                            '&::-webkit-scrollbar-track': {
                                backgroundColor: alpha('#000', 0.05),
                                borderRadius: '4px'
                            },
                        }}
                        onScroll={handleRequirementsCarouselScroll}
                    >
                        {renderPropertyCards(requirements, 'requirements', requirementsCarouselRef, isRequirementsCarouselEndReached, setIsRequirementsCarouselEndReached)}
                    </Box>
                </Paper>
            </Fade>

            {/* Statistics Section */}
            <Fade in timeout={800} style={{ transitionDelay: '450ms' }}>
                <Paper
                    elevation={0}
                    sx={{
                        mb: { xs: 1.5, sm: 2 },
                        borderRadius: { xs: 1, sm: 1.5 },
                        background: 'linear-gradient(135deg, #f5f7fa 0%, #f0f4ff 100%)',
                        p: { xs: 1.5, sm: 2 },
                        boxShadow: '0 2px 10px rgba(0,0,0,0.03)',
                    }}
                >
                    {/* Dashboard Statistics Header */}
                    <Stack
                        direction="row"
                        alignItems="center"
                        justifyContent="space-between"
                        mb={{ xs: 3, sm: 4 }}
                        sx={{
                            pb: 2,
                            borderBottom: `1px solid ${alpha('#0F52BA', 0.1)}`
                        }}
                    >
                        <Stack
                            direction="row"
                            alignItems="center"
                            spacing={2}
                        >
                            <Box
                                sx={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    width: { xs: 42, sm: 48 },
                                    height: { xs: 42, sm: 48 },
                                    borderRadius: '12px',
                                    background: 'linear-gradient(135deg, #0F52BA, #4285F4)',
                                    boxShadow: '0 4px 10px rgba(15, 82, 186, 0.25)'
                                }}
                            >
                                <TrendingUp
                                    sx={{
                                        color: 'white',
                                        fontSize: { xs: 22, sm: 26 },
                                    }}
                                />
                            </Box>
                            <Box>
                                <Typography
                                    variant="h5"
                                    sx={{
                                        fontWeight: 700,
                                        color: '#11142d',
                                        fontSize: {
                                            xs: '1.2rem',
                                            sm: '1.4rem',
                                            md: '1.6rem'
                                        },
                                        letterSpacing: '-0.02em'
                                    }}
                                >
                                    Dashboard Statistics
                                </Typography>

                            </Box>
                        </Stack>
                    </Stack>

                    {/* Dashboard Statistics Grid */}
                    <Grid
                        container
                        spacing={{ xs: 2, sm: 3, md: 4 }}
                        sx={{ mt: 1 }}
                    >
                        {pieCharts.map((chart, index) => (
                            <Grid item xs={12} sm={6} md={3} key={chart.title}>
                                <Box
                                    component={motion.div}
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.3, delay: index * 0.1 }}
                                    whileHover={{
                                        y: -5,
                                        boxShadow: '0 10px 25px rgba(15, 82, 186, 0.15)',
                                        transition: { duration: 0.2 },
                                    }}
                                    sx={{
                                        height: '100%',
                                        width: '100%',
                                        position: 'relative',
                                        minHeight: { xs: '180px', sm: '200px' },
                                    }}
                                >
                                    <PieChart
                                        title={chart.title}
                                        value={chart.value}
                                        series={chart.series}
                                        colors={["#0F52BA", "#E8F0FE"]}
                                    />
                                </Box>
                            </Grid>
                        ))}
                    </Grid>
                </Paper>
            </Fade>

            {/* Services Section */}
            <Fade in timeout={800} style={{ transitionDelay: '600ms' }}>
                <Paper
                    elevation={0}
                    sx={{
                        borderRadius: { xs: 1, sm: 1.5 },
                        background: 'linear-gradient(135deg, #f0f4ff 0%, #f5f7fa 100%)',
                        overflow: 'hidden',
                        boxShadow: '0 2px 10px rgba(0,0,0,0.03)',
                    }}
                >
                    <Service />
                </Paper>
            </Fade>
        </Container>
    );
};

export default Home;