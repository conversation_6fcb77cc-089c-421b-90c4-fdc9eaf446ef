import { useLogin } from "@refinedev/core";
import { useEffect, useRef, useState, useCallback, memo } from "react";
import Box from "@mui/material/Box";
import Container from "@mui/material/Container";
import {
  Typography,
  Grid,
  Paper,
  Card,
  CardContent,
  CircularProgress,
  Fade,
} from '@mui/material';
import { motion } from "framer-motion";
import { CredentialResponse } from "../interfaces/google";
import { Business, Search, TrendingUp, CheckCircle } from "@mui/icons-material";
import BrickBixImage from "../assets/brick-bix.png";

declare global {
  interface Window {
    google: {
      accounts: {
        id: {
          initialize: (input: any) => void;
          renderButton: (element: HTMLElement, options: any) => void;
        };
      };
    };
  }
}

const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID;

const GoogleButton: React.FC<{ onLogin: (res: CredentialResponse) => void }> = memo(({ onLogin }) => {
  const divRef = useRef<HTMLDivElement>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (typeof window === "undefined" || isInitialized) {
      return;
    }

    const initializeGoogleButton = () => {
      if (window.google && window.google.accounts && window.google.accounts.id) {
        try {
          window.google.accounts.id.initialize({
            ux_mode: "popup",
            client_id: GOOGLE_CLIENT_ID,
            callback: (res: CredentialResponse) => {
              if (res.credential) {
                setIsLoading(true);
                onLogin(res);
              }
            },
          });

          if (divRef.current instanceof HTMLElement) {
            window.google.accounts.id.renderButton(divRef.current, {
              theme: "filled_blue",
              size: "large",
              type: "standard",
              shape: "rectangular",
              text: "signin_with",
              width: 240,
              logo_alignment: "left",
            });

            setIsInitialized(true);
          }
        } catch (error) {
          console.error("Google Sign In Error:", error);
          setError("Failed to initialize Google Sign In");
        }
      } else {
        // Retry after a short delay if window.google is not available yet
        setTimeout(initializeGoogleButton, 100);
      }
    };

    initializeGoogleButton();
  }, [onLogin, isInitialized]);

  if (error) {
    return (
      <Box
        sx={{
          width: 240,
          height: 50,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          overflow: 'hidden',
          mx: 'auto',
          color: 'error.main',
          fontSize: '0.875rem'
        }}
      >
        {error}
      </Box>
    );
  }

  return (
    <Box
      sx={{
        width: 240,
        height: 50,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        overflow: 'hidden',
        mx: 'auto',
        position: 'relative'
      }}
    >
      {isLoading && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            zIndex: 2,
            borderRadius: 1
          }}
        >
          <CircularProgress size={24} />
        </Box>
      )}
      <div ref={divRef} style={{ width: '100%' }} />
    </Box>
  );
});

const FeatureCard = memo(({ image, title, description }: { image: string | undefined, title: string, description: string }) => (
  <Card
    component={motion.div}
    whileHover={{ y: -10, boxShadow: "0 10px 20px rgba(0,0,0,0.2)" }}
    sx={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      borderRadius: 2,
      overflow: 'hidden',
      boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
    }}
  >
    <Box sx={{
      display: 'flex',
      justifyContent: 'center',
      bgcolor: '#e0e0e0', // Light grey background for image section
      height: 200, // Fixed height for image section
      overflow: 'hidden' // Ensure image does not overflow
    }}>
      <Box
        component="img"
        src={image}
        alt={title}
        sx={{
          width: '100%',
          height: '100%',
          objectFit: 'cover', // Cover container, cropping if necessary
          display: 'block'
        }}
      />
    </Box>
    <CardContent sx={{ flexGrow: 1 }}>
      <Typography gutterBottom variant="h5" component="h2" sx={{ fontWeight: 'bold', mb: 2, textAlign: 'center' }}>
        {title}
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center' }}>
        {description}
      </Typography>
    </CardContent>
  </Card>
));

const ValuePropositionCard = memo(({ icon, title, description }: { icon: React.ReactNode, title: string, description: string }) => (
  <Card
    component={motion.div}
    whileHover={{ y: -10, boxShadow: "0 10px 20px rgba(0,0,0,0.2)" }}
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
    viewport={{ once: true }}
    sx={{
      display: 'flex',
      borderRadius: 2,
      overflow: 'hidden',
      boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
      mb: 2,
      p: 3,
      alignItems: 'center',
      transition: 'all 0.3s ease',
      '&:hover': {
        background: 'linear-gradient(145deg, #ffffff, #f0f0f0)',
      }
    }}
  >
    <Box sx={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      width: 60,
      height: 60,
      borderRadius: '50%',
      background: 'linear-gradient(135deg, #0F52BA 0%, #4d9ae8 100%)',
      color: 'white',
      mr: 3,
      boxShadow: '0 4px 10px rgba(15, 82, 186, 0.3)',
      transition: 'transform 0.3s ease',
      '&:hover': {
        transform: 'rotate(5deg)'
      }
    }}>
      {icon}
    </Box>
    <Box>
      <Typography
        variant="h6"
        component="h3"
        sx={{
          fontWeight: 'bold',
          mb: 1,
          background: 'linear-gradient(90deg, #0F52BA, #4d9ae8)',
          backgroundClip: 'text',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          display: 'inline-block'
        }}
      >
        {title}
      </Typography>
      <Typography variant="body2" color="text.secondary">
        {description}
      </Typography>
    </Box>
  </Card>
));


const testimonials = [
  {
    name: "Rahul Sriwas",
    role: "Real Estate Consultant",
    content: "Brickbix has made the whole process of selling my property very efficient, if you want to market your property effectively, there is no better option than brickbix.",
    avatar: "/testimonial/sriwas.jpg"
  },
  {
    name: "Sanjay Porwal",
    role: "Experienced Realtor",
    content: "I've been in the industry for 15 years, and BrickBix is a game-changer. The smart search and agent network features are invaluable for my business.",
    avatar: "/testimonial/porwal.jpg"
  },
  {
    name: "Rahul Patidar",
    role: "Real Estate Agent",
    content: "BrickBix made it easy to manage listings and build my client base.  The resources and tools are fantastic for new agents!",
    avatar: "/testimonial/patidar.jpg"
  }
];

const heroImages = [
  '/image1.jpg',
  '/image2.jpg',
  '/image3.jpg',
  '/image4.jpg',
  // Add more image URLs as needed
];


export const Login: React.FC = () => {
  const { mutate: login } = useLogin<CredentialResponse>();
  const [activeTestimonial, setActiveTestimonial] = useState(0);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [imagesLoaded, setImagesLoaded] = useState<boolean[]>(heroImages.map(() => false));
  const carouselRef = useRef<HTMLDivElement>(null);

  // Preload images
  useEffect(() => {
    heroImages.forEach((src, index) => {
      const img = new Image();
      img.src = src;
      img.onload = () => {
        setImagesLoaded(prev => {
          const newState = [...prev];
          newState[index] = true;
          return newState;
        });
      };
    });
  }, []);

  const handleLogin = useCallback((response: CredentialResponse) => {
    if (response.credential) {
      return login(response);
    }
  }, [login]);

  // Testimonial rotation
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  // Hero carousel auto-rotation
  useEffect(() => {
    // Longer duration for better user experience
    const timer = setTimeout(() => {
      setCurrentSlide((prevSlide) => (prevSlide + 1) % heroImages.length);
    }, 4000);

    return () => clearTimeout(timer);
  }, [currentSlide, heroImages.length]);

  // Manual navigation functions
  const nextSlide = () => {
    setCurrentSlide((prevSlide) => (prevSlide + 1) % heroImages.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prevSlide) => (prevSlide === 0 ? heroImages.length - 1 : prevSlide - 1));
  };


  return (
    <Box sx={{ bgcolor: '#f5f7fa', minHeight: '100vh' }}>
      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #0F52BA 0%,rgb(77, 154, 232) 100%)',
          color: 'white',
          pt: { xs: 8, md: 12 },
          pb: { xs: 10, md: 16 },
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'url("/pattern.png")',
            opacity: 0.1,
            zIndex: 0
          }
        }}
      >
        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
          <Grid container spacing={4} alignItems="center" justifyContent="center">
            <Grid item xs={12} md={6} sx={{ textAlign: 'center' }}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
              >
                <Fade in timeout={1000}>
                  <Box
                    component="img"
                    src={BrickBixImage}
                    alt="Real Estate Platform for Agents"
                    sx={{
                      width: '40%',
                      maxWidth: '500px',
                      mx: 'auto',
                      display: 'block',
                      filter: 'drop-shadow(0 10px 15px rgba(0,0,0,0.2))'
                    }}
                  />
                </Fade>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.3 }}
                >
                  <Typography
                    variant="h4"
                    sx={{
                      mt: 2,
                      mb: 3,
                      fontWeight: 'bold',
                      fontSize: { xs: '2.5rem', md: '3rem', lg: '2.5rem' },
                      textAlign: 'center',
                      lineHeight: 1.2,
                      textShadow: '0 2px 4px rgba(0,0,0,0.1)'
                    }}
                  >
                    Empowering Real Estate Agents to Thrive
                  </Typography>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.5 }}
                >
                  <Typography
                    variant="h6"
                    sx={{
                      mb: 4,
                      opacity: 0.9,
                      textAlign: 'center',
                      fontSize: { xs: '1.1rem', md: '1.3rem' },
                      maxWidth: '800px',
                      mx: 'auto'
                    }}
                  >
                    Streamline your workflow, expand your network, and close more deals with BrickBix.
                  </Typography>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.7 }}
                >
                  <Box sx={{ mt: 4, display: 'flex', justifyContent: 'center' }}>
                    <Box
                      sx={{
                        position: 'relative',
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          top: -4,
                          left: -4,
                          right: -4,
                          bottom: -4,
                          borderRadius: '8px',
                          background: 'linear-gradient(90deg, #0F52BA, #4d9ae8, #0F52BA)',
                          backgroundSize: '200% 200%',
                          animation: 'borderAnimation 10s linear infinite',
                          zIndex: 0,
                          boxShadow: '0 5px 15px rgba(15, 82, 186, 0.4)'
                        },
                        '@keyframes borderAnimation': {
                          '0%': { backgroundPosition: '0% 50%' },
                          '50%': { backgroundPosition: '100% 50%' },
                          '100%': { backgroundPosition: '0% 50%' }
                        }
                      }}
                    >
                      <Box sx={{ position: 'relative', zIndex: 1, p: 0.5, bgcolor: 'transparent' }}>
                        <GoogleButton onLogin={handleLogin} />
                      </Box>
                    </Box>
                  </Box>
                </motion.div>
              </motion.div>
            </Grid>
            <Grid item xs={12} md={6} sx={{ position: 'relative' }}>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                style={{
                  overflow: 'hidden',
                  borderRadius: 8,
                  position: 'relative',
                  width: '100%',
                  height: 0,
                  paddingBottom: '56.25%', // 16:9 aspect ratio
                  boxShadow: '0 15px 35px rgba(0,0,0,0.3)'
                }}
                ref={carouselRef}
              >
                {/* Loading indicator */}
                {!imagesLoaded.every(Boolean) && (
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '100%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: 'rgba(15, 82, 186, 0.2)',
                      zIndex: 3,
                      borderRadius: 8
                    }}
                  >
                    <CircularProgress sx={{ color: 'white' }} />
                  </Box>
                )}

                {/* Carousel images */}
                {heroImages.map((image, index) => (
                  <Fade
                    key={index}
                    in={index === currentSlide}
                    timeout={800}
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '100%',
                      display: index === currentSlide || index === (currentSlide === 0 ? heroImages.length - 1 : currentSlide - 1) ? 'block' : 'none',
                      zIndex: index === currentSlide ? 2 : 1
                    }}
                  >
                    <Box
                      component="img"
                      src={image}
                      alt={`Real Estate Platform - Slide ${index + 1}`}
                      sx={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                        borderRadius: 8
                      }}
                    />
                  </Fade>
                ))}

                {/* Navigation Arrows */}
                <Box
                  sx={{
                    position: 'absolute',
                    top: '50%',
                    left: 10,
                    transform: 'translateY(-50%)',
                    width: 40,
                    height: 40,
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    background: 'rgba(255, 255, 255, 0.3)',
                    backdropFilter: 'blur(5px)',
                    cursor: 'pointer',
                    zIndex: 2,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      background: 'rgba(255, 255, 255, 0.5)',
                    }
                  }}
                  onClick={prevSlide}
                >
                  <Box
                    component="div"
                    sx={{
                      width: 0,
                      height: 0,
                      borderTop: '6px solid transparent',
                      borderBottom: '6px solid transparent',
                      borderRight: '10px solid white',
                      marginRight: '3px'
                    }}
                  />
                </Box>

                <Box
                  sx={{
                    position: 'absolute',
                    top: '50%',
                    right: 10,
                    transform: 'translateY(-50%)',
                    width: 40,
                    height: 40,
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    background: 'rgba(255, 255, 255, 0.3)',
                    backdropFilter: 'blur(5px)',
                    cursor: 'pointer',
                    zIndex: 2,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      background: 'rgba(255, 255, 255, 0.5)',
                    }
                  }}
                  onClick={nextSlide}
                >
                  <Box
                    component="div"
                    sx={{
                      width: 0,
                      height: 0,
                      borderTop: '6px solid transparent',
                      borderBottom: '6px solid transparent',
                      borderLeft: '10px solid white',
                      marginLeft: '3px'
                    }}
                  />
                </Box>

                {/* Indicators */}
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    position: 'absolute',
                    bottom: 15,
                    left: 0,
                    right: 0,
                    zIndex: 2
                  }}
                >
                  {heroImages.map((_, index) => (
                    <Box
                      key={index}
                      onClick={() => setCurrentSlide(index)}
                      sx={{
                        width: 10,
                        height: 10,
                        borderRadius: '50%',
                        mx: 0.5,
                        bgcolor: index === currentSlide ? 'white' : 'rgba(255, 255, 255, 0.5)',
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        transform: index === currentSlide ? 'scale(1.2)' : 'scale(1)',
                        '&:hover': {
                          bgcolor: 'white',
                          transform: 'scale(1.2)'
                        }
                      }}
                    />
                  ))}
                </Box>
              </motion.div>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Value Propositions Section */}
      <Container sx={{ py: 8 }}>
        <Box textAlign="center" mb={6}>
          <Typography variant="h4" component="h2" gutterBottom sx={{ fontWeight: 'bold' }}>
            Why Choose BrickBix?
          </Typography>
          <Typography variant="h6" color="text.secondary" sx={{ maxWidth: 700, mx: 'auto' }}>
            Discover the key benefits that set BrickBix apart as the preferred platform for real estate agents.
          </Typography>
        </Box>

        <Grid container spacing={3} >
          <Grid item xs={12} md={4} lg={4}> {/* Modified Grid Item xs and lg here */}
            <ValuePropositionCard
              icon={<TrendingUp sx={{ fontSize: 56 }} />}
              title="Boost Productivity"
              description="Streamline your daily tasks and manage listings efficiently, freeing up time to focus on clients and closing deals."
            />
          </Grid>
          <Grid item xs={12} md={4} lg={4}> {/* Modified Grid Item xs and lg here */}
            <ValuePropositionCard
              icon={<Business sx={{ fontSize: 56 }} />}
              title="Expand Your Network"
              description="Connect with a wider network of agents and industry professionals to enhance collaboration and referral opportunities."
            />
          </Grid>
          <Grid item xs={12} md={4} lg={4}> {/* Modified Grid Item xs and lg here */}
            <ValuePropositionCard
              icon={<Search sx={{ fontSize: 56 }} />}
              title="Find Properties Faster"
              description="Access advanced search tools that help you quickly identify properties that meet your clients' specific requirements."
            />
          </Grid>
        </Grid>
      </Container>


      {/* Features Section with Product Glimpses */}
      <Container sx={{ py: 8 }}>
        <Box textAlign="center" mb={6}>
          <Typography variant="h4" component="h2" gutterBottom sx={{ fontWeight: 'bold' }}>
            Explore BrickBix Features
          </Typography>
          <Typography variant="h6" color="text.secondary" sx={{ maxWidth: 700, mx: 'auto' }}>
            Take a closer look at the features designed to revolutionize your real estate business.
          </Typography>
        </Box>

        <Grid container spacing={4} justifyContent="center">
          <Grid item xs={12} md={4} lg={4}>
            <FeatureCard
              image={heroImages[3]} // Product glimpse image
              title="Listing Management"
              description="Easily manage and update your property listings. Showcase properties with high-resolution images and detailed descriptions."
            />
          </Grid>
          <Grid item xs={12} md={4} lg={4}>
            <FeatureCard
              image={heroImages[0]} // Product glimpse image
              title="Agent Network"
              description="Connect and collaborate with other agents. Expand your professional network and find new opportunities."
            />
          </Grid>
          <Grid item xs={12} md={4} lg={4}>
            <FeatureCard
              image={heroImages[2]} // Product glimpse image
              title="Property & Requirement Search"
              description="Quickly search for properties with advanced filters. Find the perfect match for your clients in less time."
            />
          </Grid>

        </Grid>
      </Container>

      {/* Testimonials */}
      <Box
        sx={{
          py: 10,
          position: 'relative',
          background: 'linear-gradient(135deg, #f5f7fa 0%, #e8f0fe 100%)',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '100%',
            backgroundImage: 'radial-gradient(circle, rgba(15, 82, 186, 0.05) 1px, transparent 1px)',
            backgroundSize: '20px 20px',
            zIndex: 0
          }
        }}
      >
        <Container sx={{ position: 'relative', zIndex: 1 }}>
          {/* Section Header */}
          <Box
            component={motion.div}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            sx={{
              textAlign: 'center',
              mb: { xs: 4, md: 6 }
            }}
          >
            <Typography
              variant="h4"
              component="h2"
              gutterBottom
              sx={{
                fontWeight: 700,
                color: '#11142d',
                mb: 2,
                fontSize: { xs: '1.75rem', sm: '2rem', md: '2.25rem' },
                position: 'relative',
                display: 'inline-block',
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  bottom: -10,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: 80,
                  height: 3,
                  background: 'linear-gradient(90deg, #0F52BA, #4d9ae8)',
                  borderRadius: 1.5
                }
              }}
            >
              Trusted by Real Estate Professionals
            </Typography>
            <Typography
              variant="body1"
              color="text.secondary"
              sx={{
                maxWidth: 700,
                mx: 'auto',
                mt: 3,
                fontSize: { xs: '0.95rem', md: '1.05rem' },
                lineHeight: 1.6
              }}
            >
              Hear what real estate professionals have to say about their experience with BrickBix
            </Typography>
          </Box>

          {/* Testimonial Cards */}
          <Box
            sx={{
              position: 'relative',
              minHeight: { xs: 400, sm: 350, md: 280 },
              overflow: 'hidden',
              mt: { xs: 4, md: 6 },
              mb: { xs: 2, md: 3 }
            }}
          >
            {testimonials.map((testimonial, index) => (
              <Fade key={index} in={index === activeTestimonial} timeout={600}>
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{
                    opacity: index === activeTestimonial ? 1 : 0,
                    scale: index === activeTestimonial ? 1 : 0.95,
                    position: 'absolute',
                    width: '100%',
                    zIndex: index === activeTestimonial ? 1 : 0
                  }}
                  transition={{ duration: 0.5, ease: "easeInOut" }}
                  style={{
                    display: index === activeTestimonial ? 'block' : 'none',
                    width: '100%'
                  }}
                >
                  <Paper
                    elevation={2}
                    sx={{
                      p: { xs: 3, md: 4 },
                      borderRadius: 4,
                      background: 'linear-gradient(145deg, #ffffff, #f8f9fa)',
                      boxShadow: '0 10px 30px rgba(0,0,0,0.08)',
                      border: '1px solid rgba(0,0,0,0.03)'
                    }}
                  >
                    <Grid container spacing={{ xs: 3, md: 4 }} justifyContent="center" alignItems="center">
                      {/* Avatar Section */}
                      <Grid item xs={12} md={3} sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                        <Box
                          sx={{
                            position: 'relative',
                            width: { xs: 100, sm: 110, md: 130 },
                            height: { xs: 100, sm: 110, md: 130 },
                            borderRadius: '50%',
                            p: 0.5,
                            background: 'linear-gradient(135deg, #0F52BA, #4d9ae8)',
                            boxShadow: '0 8px 20px rgba(15, 82, 186, 0.25)'
                          }}
                        >
                          <Box
                            component="img"
                            src={testimonial.avatar}
                            alt={testimonial.name}
                            sx={{
                              width: '100%',
                              height: '100%',
                              borderRadius: '50%',
                              objectFit: 'cover',
                              border: '3px solid white'
                            }}
                          />
                        </Box>
                      </Grid>

                      {/* Content Section */}
                      <Grid item xs={12} md={9}>
                        <Box sx={{
                          position: 'relative',
                          pl: { md: 2 },
                          textAlign: { xs: 'center', md: 'left' }
                        }}>
                          {/* Quote Icon */}
                          <Box
                            sx={{
                              position: 'absolute',
                              top: { xs: -30, md: -20 },
                              left: { xs: '50%', md: -10 },
                              transform: { xs: 'translateX(-50%)', md: 'none' },
                              fontSize: { xs: '3rem', md: '4rem' },
                              color: 'rgba(15, 82, 186, 0.1)',
                              fontFamily: 'serif',
                              lineHeight: 1,
                              zIndex: 0
                            }}
                          >
                            "
                          </Box>

                          {/* Testimonial Text */}
                          <Typography
                            variant="body1"
                            paragraph
                            sx={{
                              fontStyle: 'italic',
                              mb: 2.5,
                              fontSize: { xs: '0.95rem', sm: '1rem', md: '1.1rem' },
                              lineHeight: 1.7,
                              position: 'relative',
                              zIndex: 1,
                              color: '#444',
                              fontWeight: 400,
                              letterSpacing: '0.01em',                    
                            }}
                          >
                            {testimonial.content}
                          </Typography>

                          {/* Name and Role */}
                          <Box
                            sx={{
                              mt: 3,
                              pt: 2,
                              borderTop: '1px solid rgba(0,0,0,0.06)',
                              display: 'flex',
                              flexDirection: 'column',
                              alignItems: { xs: 'center', md: 'flex-start' }
                            }}
                          >
                            <Typography
                              variant="h6"
                              sx={{
                                fontWeight: 700,
                                color: '#0F52BA',
                                fontSize: { xs: '1.1rem', md: '1.2rem' },
                                mb: 0.5
                              }}
                            >
                              {testimonial.name}
                            </Typography>
                            <Typography
                              variant="body2"
                              sx={{
                                color: 'text.secondary',
                                fontSize: { xs: '0.85rem', md: '0.9rem' },
                                fontWeight: 500
                              }}
                            >
                              {testimonial.role}
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                    </Grid>
                  </Paper>
                </motion.div>
              </Fade>
            ))}
          </Box>

          {/* Navigation Dots */}
          <Box
            component={motion.div}
            initial={{ opacity: 0, y: 10 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
            sx={{
              display: 'flex',
              justifyContent: 'center',
              mt: { xs: 3, md: 4 }
            }}
          >
            {testimonials.map((_, index) => (
              <Box
                key={index}
                onClick={() => setActiveTestimonial(index)}
                sx={{
                  width: { xs: 10, md: 12 },
                  height: { xs: 10, md: 12 },
                  borderRadius: '50%',
                  mx: 1,
                  bgcolor: index === activeTestimonial ? '#0F52BA' : 'rgba(15, 82, 186, 0.2)',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  transform: index === activeTestimonial ? 'scale(1.3)' : 'scale(1)',
                  boxShadow: index === activeTestimonial ? '0 0 10px rgba(15, 82, 186, 0.5)' : 'none',
                  '&:hover': {
                    bgcolor: index === activeTestimonial ? '#0F52BA' : 'rgba(15, 82, 186, 0.4)',
                    transform: 'scale(1.2)'
                  }
                }}
              />
            ))}
          </Box>
        </Container>
      </Box>

      {/* Call to Action */}
      <Box
        sx={{
          position: 'relative',
          py: 12,
          textAlign: 'center',
          overflow: 'hidden',
          background: 'linear-gradient(135deg, #0F52BA 0%, #4d9ae8 100%)',
          color: 'white',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: 'url("/pattern-light.png")',
            opacity: 0.1,
            zIndex: 0
          }
        }}
      >
        <Container sx={{ position: 'relative', zIndex: 1 }}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Typography
              variant="h4"
              component="h2"
              gutterBottom
              sx={{
                fontWeight: 'bold',
                fontSize: { xs: '2.5rem', md: '3rem' },
                textShadow: '0 2px 4px rgba(0,0,0,0.2)',
                mb: 3
              }}
            >
              Ready to Transform Your Real Estate Business?
            </Typography>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <Typography
              variant="h6"
              sx={{
                mb: 5,
                maxWidth: 800,
                mx: 'auto',
                opacity: 0.95,
                fontSize: { xs: '1.1rem', md: '1.2rem' },
                lineHeight: 1.6
              }}
            >
              Join BrickBix today and discover how our platform can help you streamline your business,
              connect with more clients, and achieve unparalleled success.
            </Typography>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                mb: 4
              }}
            >
              <Box
                sx={{
                  position: 'relative',
                  p: 0.5,
                  borderRadius: 2,
                  background: 'rgba(255, 255, 255, 0.2)',
                  backdropFilter: 'blur(5px)',
                  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.2)'
                }}
              >
                <GoogleButton onLogin={handleLogin} />
              </Box>
            </Box>
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
          >
            <Typography
              variant="body1"
              sx={{
                mt: 3,
                opacity: 0.9,
                fontStyle: 'italic'
              }}
            >
              Start your journey with BrickBix and elevate your real estate career.
            </Typography>
          </motion.div>

          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              mt: 5,
              gap: 2
            }}
          >
            {[1, 2, 3].map((_, index) => (
              <Box
                key={index}
                component={motion.div}
                initial={{ y: 20, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.7 + (index * 0.1) }}
                viewport={{ once: true }}
                sx={{
                  width: 40,
                  height: 40,
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  background: 'rgba(255, 255, 255, 0.2)',
                  backdropFilter: 'blur(5px)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    background: 'rgba(255, 255, 255, 0.3)',
                    transform: 'translateY(-5px)'
                  }
                }}
              >
                <CheckCircle sx={{ color: 'white', fontSize: 20 }} />
              </Box>
            ))}
          </Box>
        </Container>
      </Box>


      {/* Footer */}
      <Box
        sx={{
          bgcolor: '#1a1a1a',
          color: 'white',
          pt: 6,
          pb: 4,
          position: 'relative',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: 4,
            background: 'linear-gradient(90deg, #0F52BA, #4d9ae8, #0F52BA)',
            backgroundSize: '200% 100%',
            animation: 'gradientMove 10s linear infinite',
          },
          '@keyframes gradientMove': {
            '0%': { backgroundPosition: '0% 0%' },
            '100%': { backgroundPosition: '200% 0%' }
          }
        }}
      >
        <Container>
          <Grid container spacing={4} justifyContent="center">
            <Grid item xs={12} md={4} sx={{ textAlign: 'left' }}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
              >
                <Box
                  component="img"
                  src={BrickBixImage}
                  alt="Real Estate Platform for Agents"
                  sx={{
                    width: { xs: '120px', md: '30%' },
                    maxWidth: '120px',
                    mx: { xs: 0, md: 0 },
                    display: 'block',
                    filter: 'brightness(1.2)',
                    mb: 2
                  }}
                />
                <Typography
                  variant="body1"
                  sx={{
                    opacity: 0.9,
                    mb: 2,
                    fontWeight: 500,
                    color: '#4d9ae8',
                    textAlign: 'left'
                  }}
                >
                  The Agent-Focused Real Estate Platform
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.7, mb: 3, maxWidth: '300px', textAlign: 'left' }}>
                  Empowering real estate professionals with innovative tools to streamline their business and maximize success.
                </Typography>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={4} sx={{ textAlign: 'left' }}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
              >
                <Typography
                  variant="h6"
                  gutterBottom
                  sx={{
                    fontWeight: 'bold',
                    position: 'relative',
                    display: 'inline-block',
                    mb: 3,
                    '&::after': {
                      content: '""',
                      position: 'absolute',
                      bottom: -8,
                      left: 0,
                      width: 40,
                      height: 2,
                      bgcolor: '#4d9ae8',
                    }
                  }}
                >
                  Agent Features
                </Typography>

                {['Listing Management', 'Agent Networking', 'Client Property Search', 'Exclusive Properties'].map((feature, index) => (
                  <Box
                    key={index}
                    component={motion.div}
                    initial={{ opacity: 0, x: -10 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.2 + (index * 0.1) }}
                    viewport={{ once: true }}
                    sx={{
                      mb: 1.5,
                      display: 'flex',
                      justifyContent: 'flex-start',
                      alignItems: 'center'
                    }}
                  >
                    <Box
                      sx={{
                        width: 6,
                        height: 6,
                        borderRadius: '50%',
                        bgcolor: '#4d9ae8',
                        mr: 1.5
                      }}
                    />
                    <Typography
                      variant="body2"
                      sx={{
                        opacity: 0.8,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          color: '#4d9ae8',
                          opacity: 1
                        }
                      }}
                    >
                      {feature}
                    </Typography>
                  </Box>
                ))}
              </motion.div>
            </Grid>

            <Grid item xs={12} md={4} sx={{ textAlign: 'left' }}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <Typography
                  variant="h6"
                  gutterBottom
                  sx={{
                    fontWeight: 'bold',
                    position: 'relative',
                    display: 'inline-block',
                    mb: 3,
                    '&::after': {
                      content: '""',
                      position: 'absolute',
                      bottom: -8,
                      left: 0,
                      width: 40,
                      height: 2,
                      bgcolor: '#4d9ae8',
                    }
                  }}
                >
                  Contact
                </Typography>

                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 2,
                    alignItems: 'flex-start'
                  }}
                >
                  {[
                    { text: '<EMAIL>', icon: 'email' },
                    { text: '+91 93401 99672', icon: 'phone' },
                    { text: 'Indore, India', icon: 'location' }
                  ].map((item, index) => (
                    <Box
                      key={index}
                      component={motion.div}
                      initial={{ opacity: 0, x: -10 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.3 + (index * 0.1) }}
                      viewport={{ once: true }}
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          transform: 'translateY(-2px)'
                        }
                      }}
                    >
                      <Box
                        sx={{
                          width: 32,
                          height: 32,
                          borderRadius: '50%',
                          bgcolor: 'rgba(77, 154, 232, 0.1)',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mr: 2
                        }}
                      >
                        <Box
                          sx={{
                            width: 8,
                            height: 8,
                            borderRadius: '50%',
                            bgcolor: '#4d9ae8'
                          }}
                        />
                      </Box>
                      <Typography variant="body2" sx={{ opacity: 0.9, textAlign: 'left' }}>
                        {item.text}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              </motion.div>
            </Grid>
          </Grid>

          <Box
            sx={{
              borderTop: '1px solid rgba(255,255,255,0.1)',
              mt: 5,
              pt: 3,
              textAlign: { xs: 'left', md: 'center' }
            }}
          >
            <Typography variant="body2" sx={{ opacity: 0.6 }}>
              © {new Date().getFullYear()} BrickBix. All rights reserved.
            </Typography>
          </Box>
        </Container>
      </Box>
    </Box>
  );
};

export default Login;