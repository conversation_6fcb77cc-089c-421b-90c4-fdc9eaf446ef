import { useGetIdentity, useOne } from "@refinedev/core";
import Profile from "../components/common/Profile";
import { useActiveAuthProvider } from "@refinedev/core";
import { CircularProgress, Box, Typography } from "@mui/material";

const MyProfile = () => {
  const authProvider = useActiveAuthProvider();
  const { data: user } = useGetIdentity({
    v3LegacyAuthProviderCompatible: Boolean(authProvider?.isLegacy),
  });
  const { data, isLoading, isError } = useOne({
    resource: "users",
    id: user?.userid,
  });
  
  const myProfile = data?.data ?? {};

  if (isLoading) 
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="100vh">
        <CircularProgress />
      </Box>
    );

  if (isError) 
    return (
      <Box textAlign="center" p={4}>
        <Typography variant="h6" color="error">
          Error loading profile data
        </Typography>
      </Box>
    );

  // Check if myProfile is an object with the name property
  if (!myProfile || typeof myProfile !== "object" || !("name" in myProfile)) {
    return (
      <Box textAlign="center" p={4}>
        <Typography variant="h6">
          No profile found
        </Typography>
      </Box>
    );
  }

  return (
    <Profile
      type="My"
      name={myProfile.name}
      email={myProfile.email}
      phone={myProfile.phoneNumber || ''}
      workLocation={myProfile.workLocation || ''}
      avatar={myProfile.avatar}
      properties={myProfile.allProperties || []}
      requirement={myProfile.allRequirements || []}
      currentUserId={user?.userid}
      profileUserId={myProfile._id}
      services={myProfile.services}
      reraNumber={myProfile.reraNumber}
      isOwnProfile={true}
    />
  );
};

export default MyProfile;
