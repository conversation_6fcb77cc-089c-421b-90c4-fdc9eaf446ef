import React, { useEffect, useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import {
    WhatsApp as WhatsAppIcon,
    Facebook as FacebookIcon,
    Twitter as TwitterIcon,
    ContentCopy as CopyIcon,
    ChevronLeft as ChevronLeftIcon,
    ChevronRight as ChevronRightIcon,
    Close as CloseIcon,
    ArrowBack as ArrowBackIcon,
    Share as ShareIcon,
    LocationOn,
    Favorite,
    Bed,
    Bathtub,
    SquareFoot,
    CalendarToday,
    Description,
    KeyboardArrowRight,
    Download,
    Language,
} from "@mui/icons-material";
import {
    Typography,
    Box,
    Stack,
    Grid,
    IconButton,
    Modal,
    useMediaQuery,
    useTheme,
    Fade,
    ButtonBase,
    Paper,
    Avatar,
    Button,
    Divider,
    Tooltip,
    Chip,
    Container,
    Skeleton,
    alpha,
    Breadcrumbs,
    Link,
    Tabs,
} from "@mui/material";
import Delete from "@mui/icons-material/Delete";
import Edit from "@mui/icons-material/Edit";
import Phone from "@mui/icons-material/Phone";
import Place from "@mui/icons-material/Place";
import HomeWorkIcon from "@mui/icons-material/HomeWork";
import SquareFootIcon from "@mui/icons-material/SquareFoot";
import CustomButton from "../components/common/CustomButton";
import { useDelete, useGetIdentity, useOne } from "@refinedev/core";
import { motion, AnimatePresence } from 'framer-motion';

interface PropertyDetailsData {
    id: string;
    propertyType: string;
    photos: string[] | string;
    photo: string;
    title: string;
    location: string;
    price: number;
    totalSquareFeet: number;
    description: string;
    phone: string;
    dealType: string;
    createdAt: string;
    creator?: {
        id: string;
        name: string;
        email: string;
        avatar?: string;
        allProperties?: PropertyDetailsData[];
    };
}

function checkImage(url: string): boolean {
    const img = new Image();
    img.src = url;
    return img.width !== 0 && img.height !== 0;
}

interface ImageCarouselProps {
    images: string[];
    onClose: () => void;
}

const ImageCarousel: React.FC<ImageCarouselProps> = ({ images, onClose }) => {
    const [currentIndex, setCurrentIndex] = useState<number>(0);
    const theme = useTheme();

    const handleNext = (e: React.MouseEvent) => {
        e.stopPropagation();
        setCurrentIndex((prev) => (prev + 1) % images.length);
    };

    const handlePrevious = (e: React.MouseEvent) => {
        e.stopPropagation();
        setCurrentIndex((prev) => (prev - 1 + images.length) % images.length);
    };

    return (
        <Box
            sx={{
                position: 'relative',
                width: '100%',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'rgba(0, 0, 0, 0.95)',
            }}
            onClick={onClose}
        >
            <IconButton
                sx={{
                    position: 'absolute',
                    top: 16,
                    right: 16,
                    color: 'white',
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                    '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.7)' },
                    zIndex: 10,
                }}
                onClick={onClose}
            >
                <CloseIcon />
            </IconButton>

            <IconButton
                sx={{
                    position: 'absolute',
                    left: 16,
                    color: 'white',
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                    '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.7)' },
                    zIndex: 10,
                }}
                onClick={handlePrevious}
            >
                <ChevronLeftIcon />
            </IconButton>


                {images[currentIndex] && (
                <motion.img
                    key={currentIndex}
                    src={images[currentIndex]}
                    initial={{ opacity: 0, x: 100 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -100 }}
                    transition={{ duration: 0.3 }}
                    style={{
                        maxWidth: '90%',
                        maxHeight: '80vh',
                        objectFit: 'contain',
                        borderRadius: '8px',
                    }}
                    onClick={(e) => e.stopPropagation()}
                />
                )}


            <IconButton
                sx={{
                    position: 'absolute',
                    right: 16,
                    color: 'white',
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                    '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.7)' },
                    zIndex: 10,
                }}
                onClick={handleNext}
            >
                <ChevronRightIcon />
            </IconButton>

            <Box
                sx={{
                    position: 'absolute',
                    bottom: 16,
                    left: '50%',
                    transform: 'translateX(-50%)',
                    display: 'flex',
                    gap: 1,
                    zIndex: 10,
                }}
            >
                {images.map((_, index) => (
                    <Box
                        key={index}
                        sx={{
                            width: 10,
                            height: 10,
                            borderRadius: '50%',
                            backgroundColor: index === currentIndex ? 'white' : 'rgba(255, 255, 255, 0.5)',
                            transition: 'background-color 0.3s',
                            cursor: 'pointer',
                        }}
                        onClick={(e) => {
                            e.stopPropagation();
                            setCurrentIndex(index);
                        }}
                    />
                ))}
            </Box>
        </Box>
    );
};

const PropertyDetails: React.FC = () => {
    const navigate = useNavigate();
    const { data: user } = useGetIdentity();
    const { mutate } = useDelete();
    const { id } = useParams();
    const [propertyInfo, setPropertyInfo] = useState<PropertyDetailsData | null>(null);
    const { data: propertyData, isLoading } = useOne<PropertyDetailsData>({
        resource: "properties",
        id,
    });
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const isTablet = useMediaQuery(theme.breakpoints.down('md'));
    const [isCarouselOpen, setIsCarouselOpen] = useState<boolean>(false);
    const [mainImage, setMainImage] = useState<string>('');
    const [thumbnailImages, setThumbnailImages] = useState<string[]>([]);
    const [shareMenuOpen, setShareMenuOpen] = useState<boolean>(false);
    const [activeTab, setActiveTab] = useState(0);
    const [currentImageIndex, setCurrentImageIndex] = useState(0);

    useEffect(() => {
        if (propertyData) {
            setPropertyInfo(propertyData.data);
            if (Array.isArray(propertyData.data.photos) && propertyData.data.photos.length > 0) {
                const firstPhoto = propertyData.data.photos[0];
                if (firstPhoto) {
                    setMainImage(firstPhoto);
                }
                setThumbnailImages(propertyData.data.photos as string[]);
            } else {
                if (propertyData.data.photo) {
                    setMainImage(propertyData.data.photo);
                }
                setThumbnailImages([propertyData.data.photo]);
            }
        }
    }, [propertyData]);

    const handleImageClick = () => {
        setIsCarouselOpen(true);
    };

    const handleThumbnailClick = (imageSrc: string) => {
        setMainImage(imageSrc);
    };

    const shareProperty = (platform: string) => {
                if (!propertyInfo) return;
        const imagePreview = Array.isArray(propertyInfo.photos) && propertyInfo.photos.length > 0
            ? propertyInfo.photos[0]
            : propertyInfo.photo;
                const propertyUrl = window.location.href;
        const shareText = `Check out this amazing property on BrickBix: ${propertyInfo.title} at ${propertyInfo.location}. Price: ₹${propertyInfo.price}`;

                const shareContent = `${shareText}\n\nView here: ${propertyUrl}\n`;

                const platformUrls: { [key: string]: string } = {
                    whatsapp: `https://wa.me/?text=${encodeURIComponent(shareContent)}`,
                    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(propertyUrl)}&quote=${encodeURIComponent(shareText)}`,
                    twitter: `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareContent)}`,
                };

                if (platform && platformUrls[platform]) {
                    window.open(platformUrls[platform], '_blank');
                }
            };

    // Copy property link
    const copyPropertyLink = () => {
        navigator.clipboard.writeText(window.location.href).then(() => {
            // Provide user feedback
            alert("Link copied to clipboard!");
        }).catch(err => {
            console.error('Failed to copy: ', err);
        });
    };

    const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
        setActiveTab(newValue);
    };

    if (!propertyInfo || isLoading) {
        return (
            <Container maxWidth="lg" sx={{ py: { xs: 2, sm: 3, md: 4 } }}>
                <Paper
                    elevation={0}
                    sx={{
                        p: { xs: 2, sm: 3, md: 4 },
                        borderRadius: 2,
                        bgcolor: 'background.paper',
                        overflowX: 'auto'
                    }}
                >
                    <Grid container spacing={3} sx={{ mb: 4 }}>
                        <Grid item xs={12} md={6}>
                            <Stack spacing={2}>
                                <Skeleton variant="text" width="60%" height={40} />
                                <Skeleton variant="rectangular" width="100%" height={300} sx={{ borderRadius: 2 }} />
                                <Box sx={{ display: 'flex', gap: 1, overflowX: 'auto' }}>
                                    {[1, 2, 3, 4].map((i) => (
                                        <Skeleton key={i} variant="rectangular" width={80} height={60} sx={{ borderRadius: 1, flexShrink: 0 }} />
                                    ))}
                                </Box>
                                <Skeleton variant="text" width="80%" height={30} />
                                <Skeleton variant="text" width="100%" height={20} />
                                <Skeleton variant="text" width="90%" height={20} />
                            </Stack>
                        </Grid>
                        <Grid item xs={12} md={6}>
                            <Box sx={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                                <Stack
                                    spacing={2}
                                    alignItems="center"
                                    justifyContent="center"
                        sx={{
                                        width: '100%',
                                        p: 3,
                                        border: '1px solid',
                                        borderColor: 'divider',
                                        borderRadius: 2
                                    }}
                                >
                                    <Skeleton variant="circular" width={80} height={80} />
                                    <Skeleton variant="text" width="60%" height={30} />
                                    <Skeleton variant="text" width="40%" height={20} />
                                    <Skeleton variant="text" width="70%" height={20} />
                                    <Stack direction="row" spacing={2} sx={{ width: '100%', mt: 2 }}>
                                        <Skeleton variant="rectangular" width="50%" height={50} sx={{ borderRadius: 1 }} />
                                        <Skeleton variant="rectangular" width="50%" height={50} sx={{ borderRadius: 1 }} />
                                    </Stack>
                                </Stack>
            </Box>
                        </Grid>
                    </Grid>
                </Paper>
            </Container>
        );
    }

    const isCurrentUser = user && typeof user === 'object' && 'email' in user && propertyInfo.creator && user.email === propertyInfo.creator.email;

    const handleDeleteProperty = async () => {
        const response = window.confirm("Are you sure you want to delete this property?");

        if (response) {
            try {
                // Convert id to string to ensure proper format
                const propertyId = typeof id === 'string' ? id : String(id);

                if (!propertyId) {
                    console.error("Property ID is missing or invalid");
                    return;
                }

                console.log("Attempting to delete property with ID:", propertyId);

            mutate(
                    {
                        resource: "properties",
                        id: propertyId
                    },
                    {
                        onSuccess: () => {
                            console.log("Property deleted successfully");
                            navigate("/allProperties");
                        },
                        onError: (error) => {
                            console.error("Error deleting property:", error);
                            // Provide user feedback about the error
                            alert("Failed to delete property. Please try again later.");
                        }
                    }
                );
            } catch (error) {
                console.error("Error in delete operation:", error);
                alert("An unexpected error occurred. Please try again.");
            }
        }
    };

    return (
        <Container maxWidth="lg" sx={{ py: { xs: 2, sm: 3, md: 4 } }}>
            <Paper
            component={motion.div}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
                elevation={0}
                sx={{
                    p: { xs: 2, sm: 3, md: 4 },
                    borderRadius: 2,
                    bgcolor: 'background.paper',
                    overflowX: 'auto'
                }}
            >
                <Box sx={{ mb: 3 }}>
                    <Breadcrumbs separator="›" aria-label="breadcrumb">
                        <Link
                            component={motion.a}
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.2 }}
                            sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer', textDecoration: 'none' }}
                            color="inherit"
                            onClick={() => navigate("/allProperties")}
                        >
                            <ArrowBackIcon sx={{ mr: 0.5, fontSize: 16 }} />
                            Back to Properties
                        </Link>
                        <Typography color="text.primary">Property Details</Typography>
                    </Breadcrumbs>
                </Box>

                <Box
                    component={motion.div}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3, duration: 0.5 }}
                    sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        mb: 3,
                        flexWrap: 'wrap',
                        gap: 1
                    }}
        >
            <Typography
                variant="h4"
                        sx={{
                            fontWeight: 700,
                            color: 'text.primary',
                            fontSize: { xs: '1.5rem', sm: '1.75rem', md: '2rem' }
                        }}
            >
                <span style={{ color: theme.palette.primary.main }}>Property</span>{' '}
                <span style={{ color: theme.palette.secondary.main }}>Details</span>
            </Typography>

                    <Box sx={{ display: 'flex', gap: 1 }}>
                        <Chip
                            label={propertyInfo.propertyType}
                            color="primary"
                            icon={<HomeWorkIcon />}
                            sx={{ fontWeight: 500, borderRadius: '12px' }}
                        />
                        <Chip
                            label={propertyInfo.dealType}
                            color="secondary"
                            sx={{ fontWeight: 500, borderRadius: '12px' }}
                        />
                    </Box>
                </Box>

            <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                        <Fade in timeout={500}>
                        <Box sx={{ position: 'relative' }}>
                                <Paper
                                    component={ButtonBase}
                                onClick={handleImageClick}
                                    elevation={2}
                                sx={{
                                    width: '100%',
                                    display: 'block',
                                    aspectRatio: '16/9',
                                    overflow: 'hidden',
                                        borderRadius: '12px',
                                        transition: 'transform 0.3s ease',
                                        '&:hover': {
                                            transform: 'scale(1.01)',
                                        }
                                }}
                            >
                                <motion.img
                                    src={mainImage}
                                    alt="property_main"
                                    style={{
                                        width: '100%',
                                        height: '100%',
                                            objectFit: 'cover',
                                        backgroundColor: '#f5f5f5'
                                    }}
                                        initial={{ scale: 1 }}
                                    whileHover={{ scale: 1.05 }}
                                    transition={{ duration: 0.3 }}
                                />
                                </Paper>

                            {thumbnailImages.length > 1 && (
                                <Box sx={{
                                    mt: 2,
                                    display: 'flex',
                                    gap: 1,
                                    overflowX: 'auto',
                                    '&::-webkit-scrollbar': {
                                            height: '4px'
                                        },
                                        '&::-webkit-scrollbar-thumb': {
                                            backgroundColor: alpha(theme.palette.primary.main, 0.2),
                                            borderRadius: '4px',
                                        },
                                        '&::-webkit-scrollbar-track': {
                                            backgroundColor: alpha(theme.palette.common.black, 0.05),
                                        }
                                }}>
                                    {thumbnailImages.map((item, index) => (
                                            <Paper
                                            key={index}
                                                component={ButtonBase}
                                            onClick={() => handleThumbnailClick(item)}
                                                elevation={1}
                                            sx={{
                                                flexShrink: 0,
                                                width: { xs: '80px', sm: '100px' },
                                                height: { xs: '60px', sm: '75px' },
                                                borderRadius: '8px',
                                                overflow: 'hidden',
                                                    cursor: 'pointer',
                                                    border: mainImage === item ? `2px solid ${theme.palette.primary.main}` : 'none',
                                                    '&:hover': {
                                                        boxShadow: 3
                                                    }
                                            }}
                                        >
                                            <motion.img
                                                src={item}
                                                alt={`thumbnail-${index}`}
                                                loading="lazy"
                                                style={{
                                                    width: '100%',
                                                    height: '100%',
                                                        objectFit: 'cover',
                                                    backgroundColor: '#f5f5f5'
                                                }}
                                                whileHover={{ scale: 1.1 }}
                                                transition={{ duration: 0.2 }}
                                            />
                                            </Paper>
                                    ))}
                                </Box>
                            )}
                        </Box>
                        </Fade>

                        <Box mt={4} component={motion.div} initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ delay: 0.7 }}>
                            <Typography variant="h5" fontWeight={700} color="text.primary" gutterBottom>
                                {propertyInfo.title}
                                </Typography>

                            <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
                                <Place sx={{ color: theme.palette.text.secondary, fontSize: 20 }} />
                                <Typography variant="body1" color="text.secondary" sx={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}>
                                    {propertyInfo.location}
                                </Typography>
                            </Stack>

                            <Box
                                sx={{
                                    display: 'flex',
                                    flexWrap: 'wrap',
                                    justifyContent: 'space-between',
                                    gap: 3,
                                    mb: 3,
                                    p: 2,
                                    bgcolor: alpha(theme.palette.primary.light, 0.05),
                                    borderRadius: 2
                                }}
                            >
                                <Box>
                                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                                        Area
                                    </Typography>
                                    <Stack direction="row" alignItems="center" spacing={1}>
                                        <SquareFootIcon sx={{ color: theme.palette.primary.main, fontSize: 18 }} />
                                        <Typography
                                            variant="h6"
                                            fontWeight={600}
                                            color="text.primary"
                                            sx={{ fontSize: { xs: '1rem', sm: '1.1rem' } }}
                                        >
                                            {propertyInfo.totalSquareFeet} sq ft
                                        </Typography>
                                    </Stack>
                                </Box>

                                <Box>
                                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                                        Price
                                    </Typography>
                                    <Typography
                                        variant="h6"
                                        fontWeight={700}
                                        color="primary.main"
                                        sx={{ fontSize: { xs: '1.1rem', sm: '1.25rem' } }}
                                    >
                                        ₹ {new Intl.NumberFormat('en-IN').format(
                                            parseFloat(propertyInfo.price.toString())
                                        )}
                                    </Typography>
                                </Box>

                                <Box>
                                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                                        Listed On
                                    </Typography>
                                    <Typography variant="body1" fontWeight={500} color="text.primary">
                                        {new Date(propertyInfo.createdAt).toLocaleDateString('en-IN', {
                                            year: 'numeric',
                                            month: 'short',
                                            day: 'numeric'
                                        })}
                                    </Typography>
                                </Box>
                            </Box>

                            <Box mt={4}>
                                <Typography variant="h6" fontWeight={600} color="text.primary" gutterBottom>
                                Description
                            </Typography>
                                <Paper
                                    elevation={0}
                                    sx={{
                                        p: 2,
                                        borderRadius: 2,
                                        bgcolor: alpha(theme.palette.background.default, 0.5),
                                        border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                                    }}
                                >
                                    <Typography variant="body1" color="text.primary" sx={{ lineHeight: 1.7 }}>
                                {propertyInfo.description}
                            </Typography>
                                </Paper>
                        </Box>

                            <Box
                                sx={{
                                    mt: 4,
                                    p: 2,
                                    borderRadius: 2,
                                    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                                    display: 'flex',
                                    flexDirection: { xs: 'column', sm: 'row' },
                                    justifyContent: 'space-between',
                                    alignItems: { xs: 'flex-start', sm: 'center' },
                                    gap: 2
                                }}
                            >
                                <Box>

                        </Box>

                                <Box sx={{ display: 'flex', gap: 1 }}>
                                    <Button
                                        variant="outlined"
                                        color="primary"
                                        startIcon={<ShareIcon />}
                                        onClick={() => setShareMenuOpen(!shareMenuOpen)}
                                        sx={{
                                            borderRadius: '10px',
                                            textTransform: 'none',
                                            position: 'relative'
                                        }}
                                    >
                                        Share
                                    </Button>

                                    {shareMenuOpen && (
                                        <Paper
                                            elevation={3}
                                            sx={{
                                                position: 'absolute',
                                                mt: 5,
                                                p: 1,
                                                zIndex: 10,
                                                borderRadius: 2,
                                                display: 'flex',
                                                flexDirection: 'column',
                                                gap: 1
                                            }}
                                        >
                                            <Button
                                                size="small"
                                                startIcon={<WhatsAppIcon sx={{ color: '#25D366' }} />}
                                                onClick={() => shareProperty('whatsapp')}
                                                sx={{ justifyContent: 'flex-start', textTransform: 'none' }}
                                            >
                                                WhatsApp
                                            </Button>
                                            <Button
                                                size="small"
                                                startIcon={<FacebookIcon sx={{ color: '#4267B2' }} />}
                                                onClick={() => shareProperty('facebook')}
                                                sx={{ justifyContent: 'flex-start', textTransform: 'none' }}
                                            >
                                                Facebook
                                            </Button>
                                            <Button
                                                size="small"
                                                startIcon={<TwitterIcon sx={{ color: '#1DA1F2' }} />}
                                                onClick={() => shareProperty('twitter')}
                                                sx={{ justifyContent: 'flex-start', textTransform: 'none' }}
                                            >
                                                Twitter
                                            </Button>
                                            <Button
                                                size="small"
                                                startIcon={<CopyIcon />}
                                                onClick={copyPropertyLink}
                                                sx={{ justifyContent: 'flex-start', textTransform: 'none' }}
                                            >
                                                Copy Link
                                            </Button>
                                        </Paper>
                                    )}
                        </Box>
                        </Box>
                    </Box>
                </Grid>

                    {/* Agent Information */}
                <Grid item xs={12} md={6}>
                        <motion.div
                            initial={{ opacity: 0, x: 20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.7, delay: 0.5 }}
                        >
                            <Paper
                                elevation={1}
                                sx={{
                                    p: 3,
                                    borderRadius: 2,
                                    height: '100%',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    justifyContent: 'center',
                                    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                                    bgcolor: alpha(theme.palette.background.default, 0.5),
                                    transition: 'transform 0.3s ease',
                                    '&:hover': {
                                        transform: 'translateY(-5px)',
                                        boxShadow: 3
                                    }
                                }}
                            >
                                <Box
                                    sx={{
                                        display: 'flex',
                                        flexDirection: 'column',
                                        alignItems: 'center',
                                        mb: 3
                                    }}
                                >
                                    <Avatar
                                src={
                                    propertyInfo.creator && checkImage(propertyInfo.creator.avatar || "")
                                        ? propertyInfo.creator.avatar
                                        : "https://upload.wikimedia.org/wikipedia/commons/thumb/5/59/User-avatar.svg/2048px-User-avatar.svg.png"
                                }
                                        alt={propertyInfo.creator?.name || "Agent"}
                                        sx={{
                                            width: isMobile ? 80 : 100,
                                            height: isMobile ? 80 : 100,
                                            mb: 2,
                                            border: `3px solid ${theme.palette.primary.main}`,
                                            boxShadow: 3
                                        }}
                                    />

                                    <Typography variant="h5" fontWeight={700} color="text.primary" gutterBottom>
                                        {propertyInfo.creator?.name || "Unknown Agent"}
                            </Typography>

                                    <Chip
                                        label="Real Estate Agent"
                                        size="small"
                                        sx={{
                                            bgcolor: alpha(theme.palette.secondary.main, 0.1),
                                            color: theme.palette.secondary.main,
                                            fontWeight: 500,
                                            borderRadius: '12px'
                                        }}
                                    />

                                    <Stack direction="row" alignItems="center" spacing={1} sx={{ mt: 2 }}>
                                        <Place sx={{ color: theme.palette.text.secondary, fontSize: 16 }} />
                                        <Typography variant="body2" color="text.secondary">
                                    Indore, India
                                </Typography>
                            </Stack>
                                </Box>

                                <Divider sx={{ my: 2 }} />

                                <Box
                                    sx={{
                                        display: 'flex',
                                        justifyContent: 'center',
                                        mb: 3
                                    }}
                                >
                                    <Chip
                                        label={`${propertyInfo.creator?.allProperties?.length || 0} Properties Listed`}
                                        color="primary"
                                        variant="outlined"
                                        sx={{
                                            fontWeight: 600,
                                            px: 2,
                                            borderRadius: '12px'
                                        }}
                                    />
                                </Box>

                                <Stack spacing={2} sx={{ mt: 2 }}>
                                {isCurrentUser ? (
                                    <>
                                            <Button
                                                variant="contained"
                                                color="primary"
                                                startIcon={<Edit />}
                                                onClick={() => navigate(`/allProperties/properties/edit/${propertyInfo.id}`)}
                                                sx={{
                                                    borderRadius: '10px',
                                                    p: 1.5,
                                                    textTransform: 'none',
                                                    fontWeight: 600,
                                                    boxShadow: 2,
                                                    '&:hover': { boxShadow: 4 }
                                                }}
                                            >
                                                Edit Property
                                            </Button>
                                            <Button
                                                variant="outlined"
                                                color="error"
                                                startIcon={<Delete />}
                                                onClick={handleDeleteProperty}
                                                sx={{
                                                    borderRadius: '10px',
                                                    p: 1.5,
                                                    textTransform: 'none',
                                                    fontWeight: 600,
                                                    borderColor: theme.palette.error.main,
                                                    color: theme.palette.error.main,
                                                    '&:hover': {
                                                        backgroundColor: alpha(theme.palette.error.main, 0.1)
                                                    }
                                                }}
                                            >
                                                Delete Property
                                            </Button>
                                    </>
                                ) : (
                                    <>
                                            <Button
                                                variant="contained"
                                                color="success"
                                                startIcon={<WhatsAppIcon />}
                                                onClick={() => {
                                                    const currentUrl = window.location.href;
                                                    const message = `Hello, I'm interested in the property listed on BrickBix. Can you provide more details? Here's the link: ${currentUrl}`;
                                                const phoneNumber = `+91${propertyInfo.phone}`;
                                                window.open(`https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`, "_blank");
                                            }}
                                                sx={{
                                                    borderRadius: '10px',
                                                    p: 1.5,
                                                    textTransform: 'none',
                                                    fontWeight: 600,
                                                    boxShadow: 2,
                                                    bgcolor: '#25D366',
                                                    '&:hover': {
                                                        boxShadow: 4,
                                                        bgcolor: '#1faa50'
                                                    }
                                                }}
                                            >
                                                WhatsApp Agent
                                            </Button>
                                            <Button
                                                variant="contained"
                                                color="primary"
                                                startIcon={<Phone />}
                                                onClick={() => window.open(`tel:${propertyInfo.phone}`)}
                                                sx={{
                                                    borderRadius: '10px',
                                                    p: 1.5,
                                                    textTransform: 'none',
                                                    fontWeight: 600,
                                                    boxShadow: 2,
                                                    '&:hover': { boxShadow: 4 }
                                                }}
                                            >
                                                Call Agent
                                            </Button>
                                    </>
                                )}
                            </Stack>
                            </Paper>
                        </motion.div>
                    </Grid>
                </Grid>
            </Paper>

            <Modal
                open={isCarouselOpen}
                onClose={() => setIsCarouselOpen(false)}
                closeAfterTransition
                sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                }}
            >
                <Fade in={isCarouselOpen}>
                    <Box sx={{ outline: 'none', width: '100%', height: '100%' }}>
                        <ImageCarousel
                            images={Array.isArray(propertyInfo.photos) ? propertyInfo.photos as string[] : [propertyInfo.photo]}
                            onClose={() => setIsCarouselOpen(false)}
                        />
                    </Box>
                </Fade>
            </Modal>
        </Container>
    );
};

export default PropertyDetails;