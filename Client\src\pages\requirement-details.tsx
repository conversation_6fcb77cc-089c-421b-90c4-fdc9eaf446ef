import React, { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import {
  Typography,
  Box,
  Stack,
  Grid,
  IconButton,
  Container,
  Paper,
  Breadcrumbs,
  Link,
  Chip,
  Avatar,
  Divider,
  Button,
  Fade,
  useTheme,
  useMediaQuery,
  alpha,
  Skeleton,
} from "@mui/material";
import Delete from "@mui/icons-material/Delete";
import Edit from "@mui/icons-material/Edit";
import Phone from "@mui/icons-material/Phone";
import Place from "@mui/icons-material/Place";
import {
  WhatsApp as WhatsAppIcon,
  Facebook as FacebookIcon,
  Twitter as TwitterIcon,
  ContentCopy as CopyIcon,
  ArrowBack as ArrowBackIcon,
  Share as ShareIcon,
  AttachMoney as AttachMoneyIcon,
  HomeWork as HomeWorkIcon,
  Assignment as AssignmentIcon
} from "@mui/icons-material";
import CustomButton from "../components/common/CustomButton";
import { useDelete, useGetIdentity, useOne } from "@refinedev/core";
import BrickBix from "../assets/brick bix image.jpg";
import { motion } from "framer-motion";

interface RequirementDetailsData {
  _id: string;
  title: string;
  description: string;
  propertyType: string;
  dealType: string;
  location: string;
  askedPrice: number;
  phone: string;
  photo?: string;
  createdAt: string;
  creator?: {
    id?: string;
    name?: string;
    email?: string;
    avatar?: string;
    allRequirements?: any[];
  };
}

function checkImage(url: string): boolean {
  const img = new Image();
  img.src = url;
  return img.width !== 0 && img.height !== 0;
}

const RequirementDetails = () => {
  const navigate = useNavigate();
  const { data: user } = useGetIdentity<{ userid: string }>();
  const { mutate } = useDelete();
  const { id } = useParams();
  const [requirementInfo, setRequirementInfo] = useState<RequirementDetailsData | null>(null);
  const [shareMenuOpen, setShareMenuOpen] = useState<boolean>(false);
  const { data: requirementData, isLoading } = useOne({
    resource: "requirement",
    id,
  });
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  useEffect(() => {
    if (requirementData) {
      setRequirementInfo(requirementData.data as RequirementDetailsData);
    }
  }, [requirementData]);

  const shareProperty = (platform?: string) => {
    if (!requirementInfo) return;

    const propertyUrl = window.location.href;
    const shareText = `Check out this amazing property requirement on BrickBix: ${requirementInfo.title} at ${requirementInfo.location}. Price: ₹${requirementInfo.askedPrice}`;

    const shareContent = `${shareText}\n\nView here: ${propertyUrl}\n`;

    const platformUrls: { [key: string]: string } = {
      whatsapp: `https://wa.me/?text=${encodeURIComponent(shareContent)}`,
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(propertyUrl)}&quote=${encodeURIComponent(shareText)}`,
      twitter: `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareContent)}`,
    };

    if (platform && platformUrls[platform]) {
      window.open(platformUrls[platform], '_blank');
    }
  };

  // Copy property link
  const copyPropertyLink = () => {
    navigator.clipboard.writeText(window.location.href).then(() => {
      alert("Link copied to clipboard!");
    }).catch(err => {
      console.error('Failed to copy: ', err);
    });
  };

  if (!requirementInfo || isLoading) {
    return (
      <Container maxWidth="lg" sx={{ py: { xs: 2, sm: 3, md: 4 } }}>
        <Paper 
          elevation={0} 
          sx={{ 
            p: { xs: 2, sm: 3, md: 4 },
            borderRadius: 2,
            bgcolor: 'background.paper',
            overflowX: 'auto'
          }}
        >
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} md={6}>
              <Stack spacing={2}>
                <Skeleton variant="text" width="60%" height={40} />
                <Skeleton variant="rectangular" width="100%" height={300} sx={{ borderRadius: 2 }} />
                <Skeleton variant="text" width="80%" height={30} />
                <Skeleton variant="text" width="100%" height={20} />
                <Skeleton variant="text" width="90%" height={20} />
              </Stack>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Stack 
                  spacing={2} 
                  alignItems="center" 
                  justifyContent="center"
                  sx={{ 
                    width: '100%', 
                    p: 3, 
                    border: '1px solid',
                    borderColor: 'divider',
                    borderRadius: 2
                  }}
                >
                  <Skeleton variant="circular" width={80} height={80} />
                  <Skeleton variant="text" width="60%" height={30} />
                  <Skeleton variant="text" width="40%" height={20} />
                  <Skeleton variant="text" width="70%" height={20} />
                  <Stack direction="row" spacing={2} sx={{ width: '100%', mt: 2 }}>
                    <Skeleton variant="rectangular" width="50%" height={50} sx={{ borderRadius: 1 }} />
                    <Skeleton variant="rectangular" width="50%" height={50} sx={{ borderRadius: 1 }} />
                  </Stack>
                </Stack>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      </Container>
    );
  }

  const isCurrentUser = user && typeof user === 'object' && 'email' in user && requirementInfo.creator && user.email === requirementInfo.creator.email;

  const handleDeleteProperty = async () => {
    const response = window.confirm("Are you sure you want to delete this requirement?");
    
    if (response) {
      try {
        // Convert id to string to ensure proper format
        const requirementId = typeof id === 'string' ? id : String(id);
        
        if (!requirementId) {
          console.error("Requirement ID is missing or invalid");
          return;
        }
        
        console.log("Attempting to delete requirement with ID:", requirementId);
        
      mutate(
          { 
            resource: "requirement",
            id: requirementId
          },
          {
            onSuccess: () => {
              console.log("Requirement deleted successfully");
              navigate("/requirement");
            },
          onError: (error) => {
              console.error("Error deleting requirement:", error);
              // Provide user feedback about the error
              alert("Failed to delete requirement. Please try again later.");
          }
        }
      );
      } catch (error) {
        console.error("Error in delete operation:", error);
        alert("An unexpected error occurred. Please try again.");
      }
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: { xs: 2, sm: 3, md: 4 } }}>
      <Paper 
        component={motion.div}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        elevation={0} 
        sx={{ 
          p: { xs: 2, sm: 3, md: 4 },
          borderRadius: 2,
          bgcolor: 'background.paper',
          overflowX: 'auto'
        }}
      >
        <Box sx={{ mb: 3 }}>
          <Breadcrumbs separator="›" aria-label="breadcrumb">
            <Link 
              component={motion.a}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
              sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer', textDecoration: 'none' }} 
              color="inherit"
              onClick={() => navigate("/requirement")}
            >
              <ArrowBackIcon sx={{ mr: 0.5, fontSize: 16 }} />
              Back to Requirements
            </Link>
            <Typography color="text.primary">Requirement Details</Typography>
          </Breadcrumbs>
        </Box>

        <Box
          component={motion.div}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          sx={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            mb: 3,
            flexWrap: 'wrap',
            gap: 1
          }}
        >
          <Typography
            variant="h4" 
            sx={{ 
              fontWeight: 700, 
              color: 'text.primary',
              fontSize: { xs: '1.5rem', sm: '1.75rem', md: '2rem' } 
            }}
          >
            <span style={{ color: '#d84030' }}>Requirement</span>{' '}
            <span style={{ color: '#11418a' }}>Details</span>
          </Typography>

          <Box sx={{ display: 'flex', gap: 1 }}>
            <Chip 
              label={requirementInfo.propertyType}
              color="primary"
              icon={<HomeWorkIcon />}
              sx={{ fontWeight: 500, borderRadius: '12px' }}
            />
            <Chip 
              label={requirementInfo.dealType}
              color="secondary"
              sx={{ fontWeight: 500, borderRadius: '12px' }}
            />
          </Box>
        </Box>

        <Grid container spacing={3}>
          {/* Requirement Details Section */}
          <Grid item xs={12} md={6}>
            <Fade in timeout={500}>
              <Box>
                <Paper
                  elevation={2}
                  component={motion.div}
                  whileHover={{ scale: 1.01 }}
                  transition={{ duration: 0.3 }}
                  sx={{
                    width: '100%',
                    aspectRatio: '16/9',
                    overflow: 'hidden',
                    borderRadius: '12px',
                    mb: 4
                  }}
                >
                  <img
                    src={requirementInfo?.photo || BrickBix}
                    alt="requirement_image"
                    style={{ 
                      width: '100%', 
                      height: '100%', 
                      objectFit: 'cover',
                      transition: 'transform 0.3s ease'
                    }}
                  />
                </Paper>

                <Typography 
                  variant="h5" 
                  component={motion.h2}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.4 }}
                  fontWeight={700} 
                  color="text.primary" 
                  gutterBottom
                >
                  {requirementInfo.title}
                </Typography>
                
              <Stack
                direction="row"
                alignItems="center"
                  spacing={1} 
                  sx={{ mb: 2 }}
                  component={motion.div}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                >
                  <Place sx={{ color: theme.palette.text.secondary, fontSize: 20 }} />
                  <Typography variant="body1" color="text.secondary">
                    {requirementInfo.location}
                  </Typography>
                </Stack>

                <Box 
                  sx={{ 
                    display: 'flex', 
                    flexWrap: 'wrap',
                    justifyContent: 'space-between', 
                    gap: 3,
                    mb: 3,
                    p: 2,
                    bgcolor: alpha(theme.palette.primary.light, 0.05),
                    borderRadius: 2
                  }}
                  component={motion.div}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.6 }}
              >
                <Box>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Property Type
                  </Typography>
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <HomeWorkIcon sx={{ color: theme.palette.primary.main, fontSize: 18 }} />
                      <Typography 
                        variant="h6" 
                        fontWeight={600} 
                        color="text.primary"
                        sx={{ fontSize: { xs: '1rem', sm: '1.1rem' } }}
                      >
                        {requirementInfo.propertyType}
                    </Typography>
                  </Stack>
                </Box>

                <Box>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Budget
                  </Typography>
                    <Typography 
                      variant="h6" 
                      fontWeight={700} 
                      color="primary.main"
                      sx={{ fontSize: { xs: '1.1rem', sm: '1.25rem' } }}
                    >
                      ₹ {new Intl.NumberFormat('en-IN').format(
                        parseFloat(requirementInfo.askedPrice.toString())
                      )}
                  </Typography>
                  </Box>
                  
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Deal Type
                    </Typography>
                    <Typography variant="body1" fontWeight={500} color="text.primary">
                      {requirementInfo.dealType}
                    </Typography>
                  </Box>
                </Box>

                <Box 
                  mt={4}
                  component={motion.div}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.7 }}
                >
                  <Typography variant="h6" fontWeight={600} color="text.primary" gutterBottom>
                  Description
                </Typography>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      bgcolor: alpha(theme.palette.background.default, 0.5),
                      border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                    }}
                  >
                    <Typography variant="body1" color="text.primary" sx={{ lineHeight: 1.7 }}>
                      {requirementInfo.description}
                </Typography>
                  </Paper>
                </Box>

                <Box 
                  sx={{ 
                    mt: 4,
                    p: 2,
                    borderRadius: 2,
                    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                    display: 'flex',
                    flexDirection: { xs: 'column', sm: 'row' },
                    justifyContent: 'space-between',
                    alignItems: { xs: 'flex-start', sm: 'center' },
                    gap: 2
                  }}
                  component={motion.div}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.8 }}
                > 
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      variant="outlined"
                      color="primary"
                      startIcon={<ShareIcon />}
                      onClick={() => setShareMenuOpen(!shareMenuOpen)}
                      sx={{ 
                        borderRadius: '10px',
                        textTransform: 'none',
                        position: 'relative'
                      }}
                    >
                      Share
                    </Button>
                    
                    {shareMenuOpen && (
                      <Paper
                        elevation={3}
                        sx={{
                          position: 'absolute',
                          top: '100%',
                          mt: 5,
                          p: 1,
                          zIndex: 10,
                          borderRadius: 2,
                          display: 'flex',
                          flexDirection: 'column',
                          gap: 1
                        }}
                      >
                        <Button
                          size="small"
                          startIcon={<WhatsAppIcon sx={{ color: '#25D366' }} />}
                          onClick={() => shareProperty('whatsapp')}
                          sx={{ justifyContent: 'flex-start', textTransform: 'none' }}
                        >
                          WhatsApp
                        </Button>
                        <Button
                          size="small"
                          startIcon={<FacebookIcon sx={{ color: '#4267B2' }} />}
                          onClick={() => shareProperty('facebook')}
                          sx={{ justifyContent: 'flex-start', textTransform: 'none' }}
                        >
                          Facebook
                        </Button>
                        <Button
                          size="small"
                          startIcon={<TwitterIcon sx={{ color: '#1DA1F2' }} />}
                          onClick={() => shareProperty('twitter')}
                          sx={{ justifyContent: 'flex-start', textTransform: 'none' }}
                        >
                          Twitter
                        </Button>
                        <Button
                          size="small"
                          startIcon={<CopyIcon />}
                          onClick={copyPropertyLink}
                          sx={{ justifyContent: 'flex-start', textTransform: 'none' }}
                        >
                          Copy Link
                        </Button>
                      </Paper>
                    )}
                  </Box>
                </Box>
          </Box>
            </Fade>
        </Grid>

          {/* Agent Information */}
          <Grid item xs={12} md={6}>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7, delay: 0.5 }}
            >
              <Paper
                elevation={1}
                sx={{
                  p: 3,
                  borderRadius: 2,
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                  bgcolor: alpha(theme.palette.background.default, 0.5),
                  transition: 'transform 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-5px)',
                    boxShadow: 3
                  }
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    mb: 3
                  }}
                >
                  <Avatar
                    src={
                      requirementInfo.creator && requirementInfo.creator.avatar && checkImage(requirementInfo.creator.avatar)
                        ? requirementInfo.creator.avatar
                      : "https://upload.wikimedia.org/wikipedia/commons/thumb/5/59/User-avatar.svg/2048px-User-avatar.svg.png"
                  }
                    alt={requirementInfo.creator?.name || "Agent"}
                    sx={{
                      width: isMobile ? 80 : 100,
                      height: isMobile ? 80 : 100,
                      mb: 2,
                      border: `3px solid ${theme.palette.primary.main}`,
                      boxShadow: 3
                    }}
                  />

                  <Typography variant="h5" fontWeight={700} color="text.primary" gutterBottom>
                    {requirementInfo.creator?.name || "Unknown Agent"}
                  </Typography>

                  <Chip
                    label="Real Estate Agent"
                    size="small"
                    sx={{
                      bgcolor: alpha(theme.palette.secondary.main, 0.1),
                      color: theme.palette.secondary.main,
                      fontWeight: 500,
                      borderRadius: '12px'
                    }}
                  />

                  <Stack direction="row" alignItems="center" spacing={1} sx={{ mt: 2 }}>
                    <Place sx={{ color: theme.palette.text.secondary, fontSize: 16 }} />
                    <Typography variant="body2" color="text.secondary">
                    Indore, India
                  </Typography>
                </Stack>
                </Box>

                <Divider sx={{ my: 2 }} />

                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    mb: 3
                  }}
                >
                  <Chip
                    label={`${requirementInfo.creator?.allRequirements?.length || 0} Requirements Posted`}
                    color="primary"
                    variant="outlined"
                    sx={{ 
                      fontWeight: 600,
                      px: 2,
                      borderRadius: '12px'
                    }}
                  />
                </Box>

                <Stack spacing={2} sx={{ mt: 2 }}>
                {isCurrentUser ? (
                  <>
                      <Button
                        variant="contained"
                        color="primary"
                        startIcon={<Edit />}
                        onClick={() => navigate(`/requirement/properties-requirement/edit/${requirementInfo._id}`)}
                        sx={{
                          borderRadius: '10px',
                          p: 1.5,
                          textTransform: 'none',
                          fontWeight: 600,
                          boxShadow: 2,
                          '&:hover': { boxShadow: 4 }
                        }}
                      >
                        Edit Requirement
                      </Button>
                      <Button
                        variant="outlined"
                        color="error"
                        startIcon={<Delete />}
                        onClick={handleDeleteProperty}
                        sx={{
                          borderRadius: '10px',
                          p: 1.5,
                          textTransform: 'none',
                          fontWeight: 600,
                          borderColor: theme.palette.error.main,
                          color: theme.palette.error.main,
                          '&:hover': {
                            backgroundColor: alpha(theme.palette.error.main, 0.1)
                          }
                        }}
                      >
                        Delete Requirement
                      </Button>
                  </>
                ) : (
                  <>
                      <Button
                        variant="contained"
                        color="success"
                        startIcon={<WhatsAppIcon />}
                        onClick={() => {
                          const currentUrl = window.location.href;
                          const message = `Hello, I'm interested in the requirement listed on BrickBix. Here is the link to the requirement: ${currentUrl}`;
                          const phoneNumber = `+91${requirementInfo.phone}`;
                          window.open(`https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`, "_blank");
                        }}
                        sx={{
                          borderRadius: '10px',
                          p: 1.5,
                          textTransform: 'none',
                          fontWeight: 600,
                          boxShadow: 2,
                          bgcolor: '#25D366',
                          '&:hover': { 
                            boxShadow: 4,
                            bgcolor: '#1faa50' 
                          }
                        }}
                      >
                        WhatsApp Agent
                      </Button>
                      <Button
                        variant="contained"
                        color="primary"
                        startIcon={<Phone />}
                        onClick={() => window.open(`tel:${requirementInfo.phone}`)}
                        sx={{
                          borderRadius: '10px',
                          p: 1.5,
                          textTransform: 'none',
                          fontWeight: 600,
                          boxShadow: 2,
                          '&:hover': { boxShadow: 4 }
                        }}
                      >
                        Call Agent
                      </Button>
                  </>
                )}
              </Stack>
              </Paper>
            </motion.div>
        </Grid>
      </Grid>
      </Paper>
    </Container>
  );
};

export default RequirementDetails;