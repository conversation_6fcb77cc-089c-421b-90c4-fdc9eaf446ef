# BrickBix Linting Rules

This document outlines the linting rules and coding standards for the BrickBix project. These rules are enforced through ESLint and Prettier configurations.

## Naming Conventions

### Variables, Functions, and Methods
- Use `camelCase` for variables, functions, and methods
- Example: `getUserData`, `calculateTotal`, `isValidEmail`

### Classes, Interfaces, and React Components
- Use `PascalCase` for classes, interfaces, and React components
- Prefix interfaces with `I`
- Prefix enums with `E`
- Example: `UserProfile`, `IUserData`, `EUserRole`

### Constants
- Use `UPPER_SNAKE_CASE` for constants
- Example: `MAX_RETRY_COUNT`, `API_BASE_URL`

### Files
- Use `kebab-case` for file names
- React component files should use `PascalCase`
- Example: `user-profile.ts`, `UserProfile.tsx`, `api-utils.ts`

## Code Organization

### Import Order
Imports should be organized in the following order:
1. Built-in Node.js modules
2. External modules/libraries
3. Internal modules/components
4. Parent/sibling modules
5. Index modules
6. Type imports

Each group should be separated by a blank line, and imports should be alphabetized.

### Maximum Line Length
- Maximum line length is 100 characters
- Exceptions are made for URLs, strings, template literals, and regular expressions

### Spacing and Indentation
- Use 2 spaces for indentation
- No trailing whitespace
- Use spaces around operators
- Use spaces after commas
- No spaces inside parentheses

## Type Safety

### Function Parameters and Return Types
- All function parameters must have explicit type annotations
- All functions must have explicit return type annotations
- Example: `function getUserById(id: string): User | null`

### No Any Types
- Avoid using `any` type
- Use more specific types or `unknown` if the type is truly unknown

### Error Handling
- Use typed error handling
- Avoid throwing literals
- Use `Error` objects or custom error classes

## Documentation

### JSDoc Comments
- All functions, classes, and interfaces should have JSDoc comments
- Include descriptions for parameters and return values
- Example:
  ```typescript
  /**
   * Retrieves a user by their ID
   * @param id - The user's unique identifier
   * @returns The user object if found, null otherwise
   */
  function getUserById(id: string): User | null {
    // Implementation
  }
  ```

### Inline Comments
- Use inline comments for complex logic
- Keep comments up-to-date with code changes
- Avoid obvious comments that don't add value

## Running Linting Tools

### Client (Frontend)
```bash
# Run ESLint
npm run lint

# Fix ESLint issues automatically
npm run lint:fix

# Format code with Prettier
npm run format

# Type check TypeScript
npm run type-check
```

### Server (Backend)
```bash
# Run ESLint
npm run lint

# Fix ESLint issues automatically
npm run lint:fix

# Format code with Prettier
npm run format
```

## Commit Guidelines

When committing code, ensure that:
1. All linting rules pass
2. No TypeScript errors exist
3. Code is properly formatted

## Rationale

These linting rules were designed to:
1. Improve code readability and maintainability
2. Catch potential bugs early through static analysis
3. Ensure consistent coding style across the project
4. Enforce type safety to reduce runtime errors
5. Provide clear documentation for future developers

By following these rules, we create a codebase that is easier to understand, maintain, and extend.
