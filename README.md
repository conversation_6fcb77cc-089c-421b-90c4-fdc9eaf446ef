# BrickBix - Real Estate Platform

<div align="center">
  <img src="Client/public/logo.png" alt="BrickBix Logo" width="200"/>
  <h3>Connect Real Estate Professionals</h3>
</div>

## 📋 Overview

BrickBix is a comprehensive real estate platform designed for real estate professionals to connect, share property listings, and manage exclusive properties. The platform provides a modern, responsive interface for property search, listing management, and professional networking.

### 🌟 Key Features

- **Property Listings**: Create, view, edit, and delete property listings with detailed information
- **Exclusive Properties**: Premium property listings with enhanced details for super users
- **User Authentication**: Secure login and user management with role-based access control
- **Responsive Design**: Fully optimized for all devices, with special emphasis on mobile experience
- **Real-time Updates**: Instant property updates and notifications
- **Advanced Search**: Filter properties by location, type, price range, and more
- **Analytics Dashboard**: Track property views, shares, and user engagement
- **Secure API**: Rate-limited and protected API endpoints

## 🏗️ Architecture

BrickBix follows a modern full-stack architecture:

### Frontend (Client)
- Single Page Application (SPA) built with React and TypeScript
- Refine framework for rapid development of admin interfaces
- Material-UI (MUI) for component design with custom theming
- State management through React hooks and context
- Responsive design with mobile-first approach

### Backend (Server)
- RESTful API built with Node.js and Express
- MongoDB database with Mongoose ODM
- JWT authentication for secure access
- Cloudinary integration for image management
- Rate limiting for API protection
- Modular architecture with MVC pattern

## 🔧 Project Structure

The project is organized into two main directories:

```
BrickBix/
├── Client/                 # Frontend application
│   ├── public/             # Static assets
│   ├── src/                # Source code
│   │   ├── assets/         # Images, icons, etc.
│   │   ├── components/     # Reusable UI components
│   │   ├── config/         # Configuration files
│   │   ├── contexts/       # React contexts
│   │   ├── hooks/          # Custom React hooks
│   │   ├── interfaces/     # TypeScript interfaces
│   │   ├── layouts/        # Page layouts
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   ├── theme/          # MUI theme customization
│   │   └── utils/          # Utility functions
│   └── package.json        # Frontend dependencies
│
└── Server/                 # Backend application
    ├── controllers/        # Request handlers
    ├── middleware/         # Express middleware
    ├── models/             # Mongoose models
    ├── mongodb/            # Database connection
    ├── routes/             # API routes
    ├── services/           # Business logic
    ├── utils/              # Utility functions
    └── index.js            # Entry point
```

## 💻 Technologies Used

### Frontend (Client)
- **React 18** - JavaScript library for building user interfaces
- **TypeScript** - Typed JavaScript for better development experience
- **Refine** - React-based framework for rapid CRUD application development
- **Material UI (MUI)** - React component library with Material Design
- **Framer Motion** - Animation library for React
- **React Router** - Routing library for React
- **Axios** - HTTP client for API requests
- **React Hook Form** - Form validation and handling
- **Zod** - TypeScript-first schema validation
- **ApexCharts** - Interactive charts for analytics
- **Vite** - Build tool and development server

### Backend (Server)
- **Node.js** - JavaScript runtime
- **Express** - Web framework for Node.js
- **MongoDB** - NoSQL database
- **Mongoose** - MongoDB object modeling for Node.js
- **Cloudinary** - Cloud-based image and video management
- **Express Rate Limit** - Rate limiting middleware
- **Helmet** - Security middleware
- **CORS** - Cross-Origin Resource Sharing middleware
- **Dotenv** - Environment variable management

## 🚀 Setup and Installation

### Prerequisites
- Node.js (v16 or later)
- npm or yarn
- MongoDB (local instance or Atlas connection)
- Cloudinary account (for image uploads)

### Environment Variables

#### Client (.env)
```
VITE_API_URL=http://localhost:8080
```

#### Server (.env)
```
MONGODB_URL=your_mongodb_connection_string
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
```

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/BrickBix.git
   cd BrickBix
   ```

2. **Server Setup**
   ```bash
   cd Server
   npm install
   npm start
   ```
   The server will run on http://localhost:8080

3. **Client Setup**
   ```bash
   cd Client
   npm install
   npm run dev
   ```
   The client will run on http://localhost:5173

## 🔐 User Roles

- **Regular Users**: Can view properties, create regular property listings, and manage their own listings
- **Super Users**: Have additional privileges to create and manage exclusive property listings
  - Currently, only users with emails `<EMAIL>` and `<EMAIL>` have super user privileges

## 📱 Mobile Responsiveness

BrickBix is fully optimized for mobile devices with:
- Responsive layouts that adapt to different screen sizes
- Touch-friendly interface elements
- Compressed design for efficient space usage
- Optimized image loading for mobile networks

## 🛡️ Security Features

- JWT authentication for secure user sessions
- Rate limiting to prevent abuse
- Input validation and sanitization
- Secure file uploads with Cloudinary
- Protected API endpoints with proper authorization

## 🔄 API Endpoints

### User Endpoints
- `GET /api/v1/users` - Get all users
- `POST /api/v1/users` - Create a new user

### Property Endpoints
- `GET /api/v1/properties` - Get all properties
- `POST /api/v1/properties` - Create a new property
- `GET /api/v1/properties/:id` - Get property by ID
- `PATCH /api/v1/properties/:id` - Update property
- `DELETE /api/v1/properties/:id` - Delete property

### Exclusive Property Endpoints
- `GET /api/v1/exclusive-properties` - Get all exclusive properties
- `POST /api/v1/exclusive-properties` - Create a new exclusive property (super users only)
- `GET /api/v1/exclusive-properties/:id` - Get exclusive property by ID
- `PATCH /api/v1/exclusive-properties/:id` - Update exclusive property (super users only)
- `DELETE /api/v1/exclusive-properties/:id` - Delete exclusive property (super users only)

## 📊 Analytics

BrickBix includes analytics features to track:
- Property views
- Property shares
- User engagement
- Listing performance

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Contact

For any questions or feedback, please contact:
- Email: <EMAIL>
- Website: [brickbix.in](https://brickbix.in)
