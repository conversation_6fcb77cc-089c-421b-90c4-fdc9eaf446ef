# BrickBix Refactoring Guide

This guide explains how to use the refactoring tools and linting rules to maintain a clean, consistent codebase.

## Overview

The BrickBix project has been refactored to follow a set of consistent coding standards and linting rules. These rules are enforced through ESLint and Prettier configurations.

## Getting Started

1. Install the required dependencies:
   ```bash
   # In the Client directory
   cd Client
   npm install

   # In the Server directory
   cd ../Server
   npm install
   ```

2. Run the refactoring script to apply linting rules and format the code:
   ```bash
   # In the root directory
   node refactor.js
   ```

## Linting Rules

The project follows a set of linting rules designed to ensure code quality and consistency. These rules are documented in the [LINTING_RULES.md](./LINTING_RULES.md) file.

Key aspects of the linting rules include:
- Consistent naming conventions
- Code organization
- Type safety
- Documentation standards

## Manual Refactoring

While the automated tools can fix many issues, some refactoring tasks require manual intervention:

1. **Naming Conventions**: Ensure all variables, functions, classes, and files follow the naming conventions.
2. **Type Annotations**: Add proper type annotations to function parameters and return types.
3. **Documentation**: Add JSDoc comments to functions, classes, and interfaces.
4. **Code Organization**: Organize imports according to the specified order.

## Running Linting Tools Manually

### Client (Frontend)
```bash
# Run ESLint
npm run lint

# Fix ESLint issues automatically
npm run lint:fix

# Format code with Prettier
npm run format

# Type check TypeScript
npm run type-check
```

### Server (Backend)
```bash
# Run ESLint
npm run lint

# Fix ESLint issues automatically
npm run lint:fix

# Format code with Prettier
npm run format
```

## Continuous Integration

To ensure that all code follows the linting rules, consider setting up a CI pipeline that runs the linting tools on every pull request.

## IDE Integration

For a better development experience, configure your IDE to use the project's ESLint and Prettier configurations:

### Visual Studio Code
1. Install the ESLint and Prettier extensions
2. Configure VS Code to format on save
3. Use the workspace settings to ensure consistent formatting

### WebStorm/IntelliJ IDEA
1. Enable ESLint and Prettier in the settings
2. Configure automatic formatting on save
3. Use the project's configuration files

## Troubleshooting

If you encounter issues with the linting tools:

1. Make sure all dependencies are installed
2. Check that the configuration files are in the correct locations
3. Try running the commands manually to see detailed error messages

## Contributing

When contributing to the project, ensure that your code follows the established linting rules. Run the linting tools before submitting a pull request to catch any issues early.
