module.exports = {
  env: {
    node: true,
    es2020: true
  },
  extends: [
    "eslint:recommended",
    "plugin:import/errors",
    "plugin:import/warnings",
    "plugin:prettier/recommended"
  ],
  parserOptions: {
    ecmaVersion: "latest",
    sourceType: "module"
  },
  plugins: [
    "import",
    "prettier"
  ],
  rules: {
    // Naming conventions
    "camelcase": ["error", { "properties": "always" }],
    
    // Code organization
    "import/order": [
      "error",
      {
        "groups": [
          "builtin",
          "external",
          "internal",
          ["parent", "sibling"],
          "index",
          "object",
          "type"
        ],
        "newlines-between": "always",
        "alphabetize": {
          "order": "asc",
          "caseInsensitive": true
        }
      }
    ],
    "max-len": ["error", { 
      "code": 100, 
      "ignoreUrls": true,
      "ignoreStrings": true,
      "ignoreTemplateLiterals": true,
      "ignoreRegExpLiterals": true,
      "ignoreComments": true
    }],
    "no-console": ["warn", { "allow": ["warn", "error", "info"] }],
    "no-duplicate-imports": "error",
    
    // Documentation
    "jsdoc/require-jsdoc": [
      "warn",
      {
        "require": {
          "FunctionDeclaration": true,
          "MethodDefinition": true,
          "ClassDeclaration": true,
          "ArrowFunctionExpression": false,
          "FunctionExpression": false
        }
      }
    ],
    
    // Error handling
    "no-throw-literal": "error",
    "prefer-promise-reject-errors": "error",
    
    // Formatting (will be handled by Prettier)
    "prettier/prettier": ["error", {
      "printWidth": 100,
      "tabWidth": 2,
      "useTabs": false,
      "semi": true,
      "singleQuote": true,
      "trailingComma": "es5",
      "bracketSpacing": true,
      "arrowParens": "avoid",
      "endOfLine": "auto"
    }]
  },
  settings: {
    "import/resolver": {
      "node": {
        "extensions": [".js"]
      }
    }
  }
};
