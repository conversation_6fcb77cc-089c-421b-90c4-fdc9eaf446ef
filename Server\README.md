# BrickBix Server

This is the backend API for the BrickBix real estate platform, built with Node.js, Express, and MongoDB.

## Features

- RESTful API architecture
- MongoDB database integration
- User authentication and authorization
- Property and exclusive property management
- File uploads with Cloudinary
- Rate limiting for API protection
- Analytics tracking for property views and shares

## Tech Stack

- Node.js
- Express.js
- MongoDB with Mongoose ODM
- Cloudinary for image storage
- Express Rate Limit for API protection
- Helmet for security headers
- CORS for cross-origin resource sharing

## Getting Started

### Prerequisites

- Node.js (v16 or later)
- npm or yarn
- MongoDB (local instance or Atlas connection)
- Cloudinary account (for image uploads)

### Environment Setup

Create a `.env` file in the root directory with:

```
MONGODB_URL=your_mongodb_connection_string
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
```

### Installation

1. Install dependencies:
   ```bash
   npm install
   ```

2. Start the development server:
   ```bash
   npm start
   ```

## Project Structure

- `controllers/`: Request handlers for API endpoints
- `middleware/`: Express middleware functions
- `models/`: Mongoose data models
- `mongodb/`: Database connection setup
- `routes/`: API route definitions
- `services/`: Business logic services
- `utils/`: Utility functions
- `index.js`: Application entry point

## API Endpoints

### User Endpoints
- `GET /api/v1/users` - Get all users
- `POST /api/v1/users` - Create a new user

### Property Endpoints
- `GET /api/v1/properties` - Get all properties
- `POST /api/v1/properties` - Create a new property
- `GET /api/v1/properties/:id` - Get property by ID
- `PATCH /api/v1/properties/:id` - Update property
- `DELETE /api/v1/properties/:id` - Delete property

### Exclusive Property Endpoints
- `GET /api/v1/exclusive-properties` - Get all exclusive properties
- `POST /api/v1/exclusive-properties` - Create a new exclusive property (super users only)
- `GET /api/v1/exclusive-properties/:id` - Get exclusive property by ID
- `PATCH /api/v1/exclusive-properties/:id` - Update exclusive property (super users only)
- `DELETE /api/v1/exclusive-properties/:id` - Delete exclusive property (super users only)

## Security Features

- Input validation and sanitization
- Rate limiting to prevent abuse
- Secure file uploads with Cloudinary
- Protected API endpoints with proper authorization
- Security headers with Helmet

## Available Scripts

- `npm start`: Start the server with nodemon
- `npm run lint`: Run ESLint
- `npm run lint:fix`: Fix ESLint issues
- `npm run format`: Format code with Prettier
