import ExclusiveProperty from "../mongodb/models/exclusiveProperty.js";
import User from "../mongodb/models/user.js";
import mongoose from "mongoose";
import * as dotenv from "dotenv";
import { v2 as cloudinary } from "cloudinary";

dotenv.config();

cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

/**
 * Extract Cloudinary public ID from a URL
 * @param {string} url - Cloudinary URL
 * @returns {string|null} - Public ID or null if not a valid Cloudinary URL
 */
const getCloudinaryPublicId = (url) => {
  if (!url || typeof url !== 'string' || !url.includes('cloudinary.com')) {
    return null;
  }

  try {
    const urlParts = url.split('/');
    const fileNameWithExtension = urlParts[urlParts.length - 1];
    const publicId = fileNameWithExtension.split('.')[0];
    return publicId;
  } catch (error) {
    console.error('Error extracting public ID from Cloudinary URL:', error);
    return null;
  }
};

const getAllExclusiveProperties = async (req, res) => {
  const {
    _end,
    _order,
    _start,
    _sort,
    title_like = "",
    propertyType = "",
    location_like = "",
  } = req.query;

  const query = { active: true }; // Only return active properties by default

  if (title_like) {
    const regex = new RegExp(title_like, 'i');
    query.$or = [
      { title: regex },
      { location: regex }
    ];
  }

  if (location_like) {
    const locationRegex = new RegExp(location_like, 'i');
    if (query.$or) {
      // If $or already exists, add location to it
      query.$or.push({ location: locationRegex });
    } else {
      query.location = locationRegex;
    }
  }

  if (propertyType) {
    query.propertyType = new RegExp(propertyType, 'i');
  }

  try {
    const count = await ExclusiveProperty.countDocuments(query);

    // Handle sorting
    const sortOptions = {};
    if (_sort && _order) {
      sortOptions[_sort] = _order === 'asc' ? 1 : -1;
    } else {
      // Default sort by createdAt in descending order (newest first)
      sortOptions.createdAt = -1;
    }

    const start = parseInt(_start) || 0;
    const limit = parseInt(_end) ? parseInt(_end) - start : 10;

    const exclusiveProperties = await ExclusiveProperty.find(query)
      .sort(sortOptions)
      .skip(start)
      .limit(limit)
      .populate("creator")
      .lean();

    res.header("x-total-count", count);
    res.header("Access-Control-Expose-Headers", "x-total-count");

    res.status(200).json(exclusiveProperties);
  } catch (error) {
    console.error('Error fetching exclusive properties:', error);
    res.status(500).json({ message: 'Failed to fetch exclusive properties', error: error.message });
  }
};

const getExclusivePropertyDetail = async (req, res) => {
  try {
    const { id } = req.params;
    const propertyExists = await ExclusiveProperty.findOne({ _id: id }).populate("creator").lean();

    if (propertyExists) {
      res.status(200).json(propertyExists);
    } else {
      res.status(404).json({ message: "Exclusive property not found" });
    }
  } catch (error) {
    console.error('Error fetching exclusive property details:', error);
    if (error.name === 'CastError') {
      res.status(400).json({ message: "Invalid property ID format" });
    } else {
      res.status(500).json({ message: "Failed to fetch exclusive property details" });
    }
  }
};

const createExclusiveProperty = async (req, res) => {
  let session;
  try {
    const {
      title,
      description,
      propertyType,
      location,
      priceRange,
      typology,
      commissionRange,
      possessionDate,
      configuration,
      reraNumber,
      totalArea,
      paymentPlan,
      totalBookings,
      websiteLink,
      brochureLink,
      gpsLocation,
      floorPlanLinks,
      images, // Changed from photos to match form field name
      email,
    } = req.body;

    // Validate required fields
    if (!title || !description || !location || !priceRange || !email) {
      return res.status(400).json({ message: "All required fields must be filled." });
    }

    // Validate price range
    if (!priceRange.min || !priceRange.max || priceRange.min > priceRange.max) {
      return res.status(400).json({ message: "Invalid price range. Maximum price must be greater than minimum price." });
    }

    // Validate commission range
    if (commissionRange && (commissionRange.min > commissionRange.max)) {
      return res.status(400).json({ message: "Invalid commission range. Maximum commission must be greater than minimum commission." });
    }

    // Validate payment plan
    if (!paymentPlan || !Array.isArray(paymentPlan) || paymentPlan.length === 0) {
      return res.status(400).json({ message: "At least one payment plan installment is required." });
    }

    // Validate images
    if (!images || !Array.isArray(images) || images.length === 0) {
      return res.status(400).json({ message: "At least one image is required." });
    }

    session = await mongoose.startSession();
    session.startTransaction();

    // List of authorized super user emails
    const authorizedEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];

    // Check if the email is in the authorized list
    const isAuthorizedEmail = authorizedEmails.includes(email);

    if (!isAuthorizedEmail) {
      await session.abortTransaction();
      session.endSession();
      return res.status(403).json({
        message: "Only authorized users can create exclusive property listings.",
        authorizedEmails
      });
    }

    // Find the user
    const user = await User.findOne({ email }).session(session);
    if (!user) {
      await session.abortTransaction();
      session.endSession();
      return res.status(404).json({ message: "User not found" });
    }

    // Update user's superuser status if needed
    if (!user.isSuperUser) {
      user.isSuperUser = true;
      await user.save({ session });
      console.log(`Updated superuser status for ${email} to true`);
    }

    // Process and upload images to Cloudinary
    const photoUrls = [];
    for (const imageUrl of images) {
      // Check if it's already a URL or a base64 image
      if (imageUrl.startsWith('http')) {
        photoUrls.push(imageUrl);
        continue;
      }

      if (!imageUrl || typeof imageUrl !== 'string' || imageUrl.trim() === "") {
        continue; // Skip empty strings
      }

      try {
        const uploadResponse = await cloudinary.uploader.upload(imageUrl);
        photoUrls.push(uploadResponse.url);
      } catch (uploadError) {
        console.error("Cloudinary upload error:", uploadError);
        await session.abortTransaction();
        session.endSession();
        return res.status(500).json({ message: "Failed to upload image to Cloudinary." });
      }
    }

    if (photoUrls.length === 0) {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json({ message: "At least one valid image is required." });
    }

    // Process floor plan links
    const processedFloorPlanLinks = Array.isArray(floorPlanLinks)
      ? floorPlanLinks.filter(link => link && link.trim() !== '')
      : [];

    // Create the exclusive property
    const newExclusiveProperty = await ExclusiveProperty.create([{
      title,
      description,
      propertyType,
      location,
      priceRange,
      typology,
      commissionRange,
      possessionDate: new Date(possessionDate),
      configuration,
      reraNumber,
      totalArea,
      paymentPlan,
      totalBookings,
      websiteLink: websiteLink || '',
      brochureLink: brochureLink || '',
      gpsLocation: gpsLocation || '',
      floorPlanLinks: processedFloorPlanLinks,
      images: photoUrls, // Changed to match model field name
      creator: user._id,
      isSuperUser: true,
      active: true
    }], { session });

    // Update user's exclusive properties list
    user.allExclusiveProperties.push(newExclusiveProperty[0]._id);
    await user.save({ session });

    await session.commitTransaction();
    res.status(201).json({
      message: "Exclusive property created successfully",
      property: newExclusiveProperty[0]
    });

  } catch (error) {
    console.error('Error creating exclusive property:', error);
    if (session) await session.abortTransaction();

    if (error.name === 'ValidationError') {
      return res.status(400).json({
        message: "Validation error",
        errors: Object.values(error.errors).map(err => err.message)
      });
    }

    res.status(500).json({
      message: "Failed to create exclusive property",
      error: error.message
    });
  } finally {
    if (session) session.endSession();
  }
};

const updateExclusiveProperty = async (req, res) => {
  let session;
  try {
    const { id } = req.params;
    const {
      title,
      description,
      propertyType,
      location,
      priceRange,
      typology,
      commissionRange,
      possessionDate,
      configuration,
      reraNumber,
      totalArea,
      paymentPlan,
      totalBookings,
      websiteLink,
      brochureLink,
      gpsLocation,
      floorPlanLinks,
      images, // Changed from photos to match form field name
      email,
    } = req.body;

    // Validate required fields
    if (!title || !description || !location || !priceRange || !email) {
      return res.status(400).json({ message: "All required fields must be filled." });
    }

    // Validate price range
    if (!priceRange.min || !priceRange.max || priceRange.min > priceRange.max) {
      return res.status(400).json({ message: "Invalid price range. Maximum price must be greater than minimum price." });
    }

    // Validate commission range
    if (commissionRange && (commissionRange.min > commissionRange.max)) {
      return res.status(400).json({ message: "Invalid commission range. Maximum commission must be greater than minimum commission." });
    }

    session = await mongoose.startSession();
    session.startTransaction();

    // List of authorized super user emails
    const authorizedEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];

    // Check if the email is in the authorized list
    const isAuthorizedEmail = authorizedEmails.includes(email);

    if (!isAuthorizedEmail) {
      await session.abortTransaction();
      session.endSession();
      return res.status(403).json({
        message: "Only authorized users can update exclusive property listings.",
        authorizedEmails
      });
    }

    // Find the user
    const user = await User.findOne({ email }).session(session);
    if (!user) {
      await session.abortTransaction();
      session.endSession();
      return res.status(404).json({ message: "User not found" });
    }

    // Update user's superuser status if needed
    if (!user.isSuperUser) {
      user.isSuperUser = true;
      await user.save({ session });
      console.log(`Updated superuser status for ${email} to true`);
    }

    // Find the property
    const property = await ExclusiveProperty.findById(id).session(session);
    if (!property) {
      await session.abortTransaction();
      session.endSession();
      return res.status(404).json({ message: "Exclusive property not found" });
    }

    // Special <NAME_EMAIL> to update any property
    if (email !== '<EMAIL>' && property.creator.toString() !== user._id.toString()) {
      await session.abortTransaction();
      session.endSession();
      return res.status(403).json({ message: "You are not authorized to update this property." });
    }

    // Process images - keep existing ones and add new ones
    let photoUrls = [];

    // If no new images provided, keep existing ones
    if (!images || !Array.isArray(images) || images.length === 0) {
      photoUrls = property.images;
    } else {
      // Find images that were in the old array but not in the new one (these need to be deleted from Cloudinary)
      const oldImageUrls = property.images || [];

      // Filter out only HTTP URLs from the new images array (these are existing images)
      const newHttpImageUrls = images.filter(img => img && typeof img === 'string' && img.startsWith('http'));

      // Identify images that were removed
      const removedImages = oldImageUrls.filter(oldUrl => !newHttpImageUrls.includes(oldUrl));

      // Delete removed images from Cloudinary
      for (const imageUrl of removedImages) {
        try {
          const publicId = getCloudinaryPublicId(imageUrl);
          if (publicId) {
            await cloudinary.uploader.destroy(publicId);
          }
        } catch (cloudinaryError) {
          // Continue with other operations even if deletion fails
        }
      }

      // Process the new image array
      for (const image of images) {
        // Check if it's already a URL or a base64 image
        if (image && typeof image === 'string' && image.startsWith('http')) {
          photoUrls.push(image); // Keep existing URL
          continue;
        }

        if (!image || typeof image !== 'string' || image.trim() === "") {
          continue; // Skip empty strings
        }

        try {
          const uploadResponse = await cloudinary.uploader.upload(image);
          photoUrls.push(uploadResponse.url);
        } catch (uploadError) {
          // Continue with other uploads even if one fails
        }
      }
    }

    // If no valid images after processing, keep existing ones
    if (photoUrls.length === 0) {
      photoUrls = property.images;
    }

    // Validate that we have at least one image
    if (photoUrls.length === 0) {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json({ message: "At least one valid image is required." });
    }

    // Process floor plan links
    const processedFloorPlanLinks = Array.isArray(floorPlanLinks)
      ? floorPlanLinks.filter(link => link && link.trim() !== '')
      : property.floorPlanLinks || [];

    // Prepare update data
    const updateData = {
      title,
      description,
      propertyType,
      location,
      priceRange: {
        min: typeof priceRange.min === 'string' ? parseFloat(priceRange.min) : priceRange.min,
        max: typeof priceRange.max === 'string' ? parseFloat(priceRange.max) : priceRange.max
      },
      typology,
      commissionRange: {
        min: typeof commissionRange.min === 'string' ? parseFloat(commissionRange.min) : commissionRange.min,
        max: typeof commissionRange.max === 'string' ? parseFloat(commissionRange.max) : commissionRange.max
      },
      possessionDate: new Date(possessionDate),
      configuration,
      reraNumber,
      totalArea: typeof totalArea === 'string' ? parseFloat(totalArea) : totalArea,
      paymentPlan,
      totalBookings: typeof totalBookings === 'string' ? parseInt(totalBookings) : totalBookings,
      websiteLink: websiteLink || '',
      brochureLink: brochureLink || '',
      gpsLocation: gpsLocation || '',
      floorPlanLinks: processedFloorPlanLinks,
      images: photoUrls,
      updatedAt: new Date(Date.now() + 19800000) // Update timestamp to IST
    };

    try {
      // Update the property
      const updatedProperty = await ExclusiveProperty.findByIdAndUpdate(
        { _id: id },
        updateData,
        { new: true, runValidators: true, session }
      );

      if (!updatedProperty) {
        await session.abortTransaction();
        session.endSession();
        return res.status(404).json({ message: "Property not found during update operation" });
      }

      await session.commitTransaction();
      res.status(200).json({
        message: "Exclusive property updated successfully",
        property: updatedProperty
      });
    } catch (updateError) {
      await session.abortTransaction();
      throw updateError; // This will be caught by the outer catch block
    }
  } catch (error) {
    if (session) await session.abortTransaction();

    if (error.name === 'ValidationError') {
      return res.status(400).json({
        message: "Validation error",
        errors: Object.values(error.errors).map(err => err.message)
      });
    } else if (error.name === 'CastError') {
      return res.status(400).json({ message: "Invalid property ID format" });
    }

    res.status(500).json({
      message: "Failed to update exclusive property",
      error: error.message
    });
  } finally {
    if (session) session.endSession();
  }
};

const deleteExclusiveProperty = async (req, res) => {
  let session;
  try {
    const { id } = req.params;
    const { email } = req.body;

    // List of authorized super user emails
    const authorizedEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];

    // Check if the email is in the authorized list
    const isAuthorizedEmail = authorizedEmails.includes(email);

    if (!isAuthorizedEmail) {
      return res.status(403).json({
        message: "Only authorized users can delete exclusive property listings.",
        authorizedEmails
      });
    }

    // Find the user
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Update user's superuser status if needed
    if (!user.isSuperUser) {
      user.isSuperUser = true;
      await user.save();
    }

    // Find the property
    const propertyToDelete = await ExclusiveProperty.findById(id).populate("creator");
    if (!propertyToDelete) {
      return res.status(404).json({ message: "Exclusive property not found" });
    }

    // Special <NAME_EMAIL> to delete any property
    if (email !== '<EMAIL>' && propertyToDelete.creator._id.toString() !== user._id.toString()) {
      return res.status(403).json({ message: "You are not authorized to delete this property." });
    }

    session = await mongoose.startSession();
    session.startTransaction();

    // Delete images from Cloudinary
    if (propertyToDelete.images && Array.isArray(propertyToDelete.images)) {
      for (const imageUrl of propertyToDelete.images) {
        try {
          const publicId = getCloudinaryPublicId(imageUrl);
          if (publicId) {
            await cloudinary.uploader.destroy(publicId);
          }
        } catch (cloudinaryError) {
          // Continue with other deletions even if one fails
        }
      }
    }

    // Fallback for old schema (if it exists)
    else if (propertyToDelete.photos && Array.isArray(propertyToDelete.photos)) {
      for (const photoUrl of propertyToDelete.photos) {
        try {
          const publicId = getCloudinaryPublicId(photoUrl);
          if (publicId) {
            await cloudinary.uploader.destroy(publicId);
          }
        } catch (cloudinaryError) {
          // Continue with other deletions even if one fails
        }
      }
    }

    // Delete the property
    await propertyToDelete.deleteOne({ session });

    // Update user's exclusive properties list
    if (propertyToDelete.creator) {
      propertyToDelete.creator.allExclusiveProperties.pull(propertyToDelete._id);
      await propertyToDelete.creator.save({ session });
    }

    await session.commitTransaction();
    res.status(200).json({ message: "Exclusive property and all images deleted successfully" });
  } catch (error) {
    if (session) await session.abortTransaction();
    res.status(500).json({
      message: "Failed to delete exclusive property",
      details: error.message
    });
  } finally {
    if (session) session.endSession();
  }
};

// Check if user is a super user
const checkSuperUser = async (req, res) => {
  try {
    const { email } = req.params;

    if (!email) {
      return res.status(400).json({ message: "Email is required" });
    }

    // List of authorized super user emails
    const authorizedEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];

    // Check if the email is in the authorized list
    const isAuthorizedEmail = authorizedEmails.includes(email);

    // Find the user in the database
    const user = await User.findOne({ email });

    if (!user) {
      // If user doesn't exist but email is authorized, return true
      if (isAuthorizedEmail) {
        return res.status(200).json({
          isSuperUser: true,
          authorizedEmails,
          message: "Email is authorized but user not found in database"
        });
      }

      return res.status(404).json({
        message: "User not found",
        isSuperUser: false,
        authorizedEmails
      });
    }

    // If user exists but superuser status doesn't match authorization list, update it
    if (user.isSuperUser !== isAuthorizedEmail) {
      user.isSuperUser = isAuthorizedEmail;
      await user.save();
    }

    return res.status(200).json({
      isSuperUser: user.isSuperUser,
      authorizedEmails,
      email
    });
  } catch (error) {
    res.status(500).json({ message: "Failed to check super user status" });
  }
};

// Track property share
const trackPropertyShare = async (req, res) => {
  try {
    const { id } = req.params;
    const { platform } = req.body;

    const property = await ExclusiveProperty.findById(id);
    if (!property) {
      return res.status(404).json({ message: "Exclusive property not found" });
    }

    // Increment share count
    property.shareCount = (property.shareCount || 0) + 1;

    // Save the updated property
    await property.save();

    res.status(200).json({
      message: "Share tracked successfully",
      shareCount: property.shareCount,
      platform
    });
  } catch (error) {
    res.status(500).json({ message: "Failed to track property share" });
  }
};

// Track property view
const trackPropertyView = async (req, res) => {
  try {
    const { id } = req.params;

    const property = await ExclusiveProperty.findById(id);
    if (!property) {
      return res.status(404).json({ message: "Exclusive property not found" });
    }

    // Increment view count
    property.viewCount = (property.viewCount || 0) + 1;

    // Save the updated property
    await property.save();

    res.status(200).json({
      message: "View tracked successfully",
      viewCount: property.viewCount
    });
  } catch (error) {
    res.status(500).json({ message: "Failed to track property view" });
  }
};

// Get property by slug
const getExclusivePropertyBySlug = async (req, res) => {
  try {
    const { slug } = req.params;
    const propertyExists = await ExclusiveProperty.findOne({ slug }).populate("creator").lean();

    if (propertyExists) {
      res.status(200).json(propertyExists);
    } else {
      res.status(404).json({ message: "Exclusive property not found" });
    }
  } catch (error) {
    res.status(500).json({ message: "Failed to fetch exclusive property details" });
  }
};

export {
  getAllExclusiveProperties,
  getExclusivePropertyDetail,
  getExclusivePropertyBySlug,
  createExclusiveProperty,
  updateExclusiveProperty,
  deleteExclusiveProperty,
  checkSuperUser,
  trackPropertyShare,
  trackPropertyView,
};