import Property from "../mongodb/models/property.js";
import User from "../mongodb/models/user.js";
import mongoose from "mongoose";
import * as dotenv from "dotenv";
import { v2 as cloudinary } from "cloudinary";

dotenv.config();

cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

const getAllProperties = async (req, res) => {
  const {
    _end,
    _order,
    _start,
    _sort,
    title_like = "",
    propertyType = "",
    userId = "", // Add userId parameter to filter user's own properties
  } = req.query;

  const query = {};

  // If userId is provided, show all properties for that user (public + private)
  // Otherwise, show only public properties
  if (userId) {
    query.$or = [
      { isPublic: true }, // Public properties visible to everyone
      { isPublic: { $exists: false } }, // Legacy properties without isPublic field (default to public)
      { creator: userId } // User's own properties (both public and private)
    ];
  } else {
    query.$or = [
      { isPublic: true }, // Public properties
      { isPublic: { $exists: false } } // Legacy properties without isPublic field (default to public)
    ];
  }

  if (title_like) {
    const regex = new RegExp(title_like, 'i');
    const titleLocationQuery = {
      $or: [
        { title: regex },
        { location: regex }
      ]
    };

    // Combine with existing query
    if (query.$or) {
      query.$and = [
        { $or: query.$or },
        titleLocationQuery
      ];
      delete query.$or;
    } else {
      query.$and = [
        { isPublic: true },
        titleLocationQuery
      ];
      delete query.isPublic;
    }
  }

  if (propertyType) {
    query.propertyType = propertyType.toLowerCase();
  }

  try {
    const count = await Property.countDocuments(query);
    const start = parseInt(_start) || 0;
    const limit = parseInt(_end) ? parseInt(_end) - start : 10;

    const properties = await Property.find(query)
      .sort({ [_sort]: _order })
      .skip(start)
      .limit(limit)
      .populate("creator", "name email")
      .lean();

    res.header("x-total-count", count);
    res.header("Access-Control-Expose-Headers", "x-total-count");

    res.status(200).json(properties);
  } catch (error) {
    console.error('Error fetching properties:', error);
    res.status(500).json({ message: 'Failed to fetch properties' });
  }
};

const getPropertyDetail = async (req, res) => {
  try {
    const { id } = req.params;
    const { userId } = req.query; // Get userId from query params

    const propertyExists = await Property.findOne({ _id: id }).populate("creator").lean();

    if (!propertyExists) {
      return res.status(404).json({ message: "Property not found" });
    }

    // Check visibility permissions
    // If isPublic doesn't exist (legacy property), treat as public
    // If isPublic is false and user is not the creator, deny access
    const isPublic = propertyExists.isPublic !== undefined ? propertyExists.isPublic : true;
    const creatorId = propertyExists.creator._id.toString();

    // Allow access if property is public OR if user is the creator
    if (!isPublic && userId && creatorId !== userId) {
      return res.status(403).json({ message: "You don't have permission to view this property" });
    }

    res.status(200).json(propertyExists);
  } catch (error) {
    console.error('Error fetching property details:', error);
    if (error.name === 'CastError') {
      res.status(400).json({ message: "Invalid property ID format" });
    } else {
      res.status(500).json({ message: "Failed to fetch property details" });
    }
  }
};


const createProperty = async (req, res) => {
    let session;
    try {
        const {
            title,
            description,
            propertyType,
            dealType,
            location,
            price,
            phone,
            photos, // Changed from photo to photos (array)
            email,
            totalSquareFeet,
            isPublic = true, // Add visibility field with default true
        } = req.body;

        if (!title || !description || !location || !price || !email || !totalSquareFeet) {
            return res.status(400).json({ message: "All fields are required." });
        }
        if (!photos || !Array.isArray(photos) || photos.length === 0) { // Validate photos array
            return res.status(400).json({ message: "At least one property photo is required." });
        }
        if (photos.length > 5) { // Validate max 5 photos
            return res.status(400).json({ message: "You can upload a maximum of 5 photos." });
        }

        session = await mongoose.startSession();
        session.startTransaction();

        const user = await User.findOne({ email }).session(session);
        if (!user) throw new Error("User not found");

        // --- Cloudinary Connection Test ---
        try {
            // Cloudinary connection test - try a simple upload (e.g., a placeholder image)
            const testUpload = await cloudinary.uploader.upload("https://cloudinary.com/images/old_logo.png");
            console.log("Cloudinary test upload successful:", testUpload); // Log success
        } catch (cloudinaryError) {
            console.error("Cloudinary connection test failed:", cloudinaryError); // Log failure
            return res.status(500).json({ message: "Failed to connect to Cloudinary. Check your configuration.", error: cloudinaryError.message });
        }
        // --- End Cloudinary Connection Test ---


        const photoUrls = []; // Array to store Cloudinary URLs
        for (const photoBase64 of photos) { // Loop through each base64 photo string
            // --- Basic Base64 Data Validation ---
            if (!photoBase64 || typeof photoBase64 !== 'string' || photoBase64.trim() === "") {
                console.error("Invalid photoBase64 data:", photoBase64); // Log invalid data
                await session.abortTransaction(); // Abort transaction due to invalid data
                session.endSession();
                return res.status(400).json({ message: "Invalid image data received.", error: "Invalid Base64 string provided" });
            }
            // --- End Basic Base64 Data Validation ---

            try {
                const uploadResponse = await cloudinary.uploader.upload(photoBase64);
                photoUrls.push(uploadResponse.url); // Add each URL to the array
            } catch (uploadError) {
                console.error("Cloudinary upload error:", uploadError); // Log specific Cloudinary upload error
                await session.abortTransaction(); // Abort transaction if Cloudinary upload fails
                session.endSession();
                return res.status(500).json({ message: "Failed to upload image to Cloudinary.", error: uploadError.message }); // Return 500 with specific Cloudinary error
            }
        }

        const newProperty = await Property.create({
            title,
            description,
            propertyType,
            dealType,
            location,
            price,
            totalSquareFeet,
            phone,
            photos: photoUrls, // Store the array of photo URLs
            creator: user._id,
            isPublic: Boolean(isPublic), // Add visibility field
        });

        user.allProperties.push(newProperty._id);
        await user.save({ session });

        await session.commitTransaction();
        res.status(201).json({ message: "Property created successfully" });

    } catch (error) {
        console.error('Error creating property:', error); // Log full error object - crucial for debugging
        if (session) await session.abortTransaction();
        if (error.message === "User not found") {
            res.status(404).json({ message: "User not found" });
        } else if (error.message === "Invalid image data received.") { // Specific error from Base64 validation
            res.status(400).json({ message: "Invalid image data received.", error: error.message }); // Return 400 for invalid input
        }
        else {
            res.status(500).json({ message: "Failed to create property", error: error.message }); // Include error message in 500 response
        }
    } finally {
        if (session) session.endSession();
    }
};


const updateProperty = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      title,
      description,
      propertyType,
      dealType,
      location,
      price,
      phone,
      photos, // Expecting an array of photos now
      totalSquareFeet,
      isPublic = true, // Add visibility field with default true
    } = req.body;

    if (!title || !description || !location || !price || !totalSquareFeet) {
      return res.status(400).json({ message: "All fields are required." });
    }

    let photoUrls = []; // Array to store Cloudinary URLs
    if (photos && Array.isArray(photos)) {
      // Process each photo if it's a data URL
      for (const photo of photos) {
        if (photo && photo.startsWith('data:')) {
          const uploadResponse = await cloudinary.uploader.upload(photo);
          photoUrls.push(uploadResponse.url);
        } else if (photo) {
          photoUrls.push(photo); // Assume it's already a valid URL or keep existing URL
        }
      }
    }

    const updatedProperty = await Property.findByIdAndUpdate(
      { _id: id },
      {
        title,
        description,
        propertyType,
        dealType,
        phone,
        location,
        price,
        photos: photoUrls, // Store the array of URLs
        totalSquareFeet,
        isPublic: Boolean(isPublic), // Add visibility field
      },
      { new: true, runValidators: true }
    );

    if (!updatedProperty) {
      return res.status(404).json({ message: "Property not found" });
    }

    res.status(200).json({ message: "Property updated successfully", property: updatedProperty });
  } catch (error) {
    console.error('Error updating property:', error);
    if (error.name === 'ValidationError') {
      res.status(400).json({ message: "Invalid input data", errors: error.errors });
    } else if (error.name === 'CastError') {
      res.status(400).json({ message: "Invalid property ID format" });
    } else {
      res.status(500).json({ message: "Failed to update property" });
    }
  }
};

const deleteProperty = async (req, res) => {
  let session;
  try {
    const { id } = req.params;

    const propertyToDelete = await Property.findById(id).populate("creator");

    if (!propertyToDelete) {
      return res.status(404).json({ message: "Property not found" });
    }

    session = await mongoose.startSession();
    session.startTransaction();

    // Handle photos array (current schema)
    if (propertyToDelete.photos && Array.isArray(propertyToDelete.photos) && propertyToDelete.photos.length > 0) {
      console.log(`Deleting ${propertyToDelete.photos.length} images from Cloudinary`);
      
      for (const photoUrl of propertyToDelete.photos) {
        try {
          const urlParts = photoUrl.split('/');
          const fileNameWithExtension = urlParts[urlParts.length - 1];
          const publicId = fileNameWithExtension.split('.')[0];
          
          console.log(`Deleting Cloudinary image with public_id: ${publicId}`);
          await cloudinary.uploader.destroy(publicId);
        } catch (cloudinaryError) {
          console.error('Error deleting image from Cloudinary:', cloudinaryError);
          // Continue with other deletions even if one fails
        }
      }
    } 
    // Fallback for single photo field (old schema)
    else if (propertyToDelete.photo) {
      try {
        const publicId = propertyToDelete.photo.split('/').slice(-1)[0].split('.')[0];
        console.log(`Deleting single Cloudinary image with public_id: ${publicId}`);
        await cloudinary.uploader.destroy(publicId);
      } catch (cloudinaryError) {
        console.error('Error deleting single image from Cloudinary:', cloudinaryError);
      }
    }

    // Delete the property document
    await propertyToDelete.deleteOne({ session });
    
    // Update creator reference
    if (propertyToDelete.creator) {
      propertyToDelete.creator.allProperties.pull(propertyToDelete._id);
      await propertyToDelete.creator.save({ session });
    }

    await session.commitTransaction();
    res.status(200).json({ message: "Property and all images deleted successfully" });
  } catch (error) {
    console.error('Error deleting property:', error);
    if (session) await session.abortTransaction();
    res.status(500).json({ 
      message: "Failed to delete property",
      details: error.message
    });
  } finally {
    if (session) session.endSession();
  }
};

const getTopLatestProperties = async (req, res) => {
  try {
    // Only get public properties for the dashboard (including legacy properties)
    const latestProperties = await Property.aggregate([
      {
        $match: {
          $or: [
            { isPublic: true },
            { isPublic: { $exists: false } } // Include legacy properties
          ]
        }
      },
      { $sample: { size: 3 } }
    ]).exec();

    if (latestProperties.length === 0) {
      return res.status(404).json({ message: 'No properties found' });
    }

    const publicQuery = { $or: [{ isPublic: true }, { isPublic: { $exists: false } }] };
    const totalPropertiesCount = await Property.countDocuments(publicQuery);
    const commercialPropertiesCount = await Property.countDocuments({
      ...publicQuery,
      propertyType: 'commercial'
    });
    const apartmentPropertiesCount = await Property.countDocuments({
      ...publicQuery,
      propertyType: 'apartment'
    });

    res.status(200).json({
      properties: latestProperties,
      totalPropertiesCount,
      commercialPropertiesCount,
      apartmentPropertiesCount
    });
  } catch (error) {
    console.error('Error fetching latest properties:', error);
    res.status(500).json({ message: 'Failed to fetch latest properties' });
  }
};

export {
  getAllProperties,
  getPropertyDetail,
  createProperty,
  updateProperty,
  deleteProperty,
  getTopLatestProperties,
};