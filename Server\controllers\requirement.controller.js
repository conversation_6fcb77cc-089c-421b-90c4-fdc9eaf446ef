import mongoose from 'mongoose';
import RequirementModel from '../mongodb/models/requirment.js';
import User from '../mongodb/models/user.js';

const getAllRequirements = async (req, res) => {
  const {
    _end,
    _order,
    _start,
    _sort,
    title_like = "",
    propertyType = "",
    userId = "", // Add userId parameter to filter user's own requirements
  } = req.query;

  const query = {};

  // If userId is provided, show all requirements for that user (public + private)
  // Otherwise, show only public requirements
  if (userId) {
    query.$or = [
      { isPublic: true }, // Public requirements visible to everyone
      { isPublic: { $exists: false } }, // Legacy requirements without isPublic field (default to public)
      { creator: userId } // User's own requirements (both public and private)
    ];
  } else {
    query.$or = [
      { isPublic: true }, // Public requirements
      { isPublic: { $exists: false } } // Legacy requirements without isPublic field (default to public)
    ];
  }

  if (title_like) {
    const regex = new RegExp(title_like, 'i');
    const titleLocationQuery = {
      $or: [
        { title: regex },
        { location: regex }
      ]
    };

    // Combine with existing query
    if (query.$or) {
      query.$and = [
        { $or: query.$or },
        titleLocationQuery
      ];
      delete query.$or;
    } else {
      query.$and = [
        { isPublic: true },
        titleLocationQuery
      ];
      delete query.isPublic;
    }
  }

  if (propertyType) {
    query.propertyType = propertyType.toLowerCase();
  }

  try {
    const count = await RequirementModel.countDocuments(query);
    const start = parseInt(_start) || 0;
    const limit = parseInt(_end) ? parseInt(_end) - start : 10;

    const requirements = await RequirementModel.find(query)
    .sort({ [_sort]: _order })  // Sort the documents by `_sort` field in `_order` direction
    .skip(start)   // Skip the first `start` items
    .limit(limit) // Limit the result to `limit` items
    .populate("creator", "name email"); // Populate creator info

    // Set response headers to include the total count
    res.header("x-total-count", count);
    res.header("Access-Control-Expose-Headers", "x-total-count");

    res.status(200).json(requirements);
  } catch (error) {
    console.error('Error fetching requirements:', error);
    res.status(500).json({ message: 'Failed to fetch requirements' });
  }
};

const saveRequirement = async (req, res) => {
  const { title, description, propertyType, dealType, phone, askedPrice, location, email, isPublic = true } = req.body;

  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    const user = await User.findOne({ email }).session(session);
    if (!user) {
      throw new Error('User not found');
    }

    const requirement = await RequirementModel.create({
      title,
      description,
      propertyType,
      dealType,
      askedPrice,
      phone,
      location,
      creator: user._id,
      isPublic: Boolean(isPublic), // Add visibility field
    });

    user.allRequirements.push(requirement);
    await user.save({ session });

    await session.commitTransaction();
    res.status(201).json({ message: 'Requirement created successfully', requirement });
  } catch (error) {
    await session.abortTransaction();
    console.error('Error saving requirement:', error);
    if (error.message === 'User not found') {
      res.status(404).json({ message: 'User not found' });
    } else {
      res.status(500).json({ message: 'Failed to save requirement' });
    }
  } finally {
    session.endSession();
  }
};

const getRequirementById = async (req, res) => {
  try {
    const { id } = req.params;
    const { userId } = req.query; // Get userId from query params

    const requirement = await RequirementModel.findOne({ _id: id }).populate('creator').lean();

    if (!requirement) {
      return res.status(404).json({ message: 'Requirement not found' });
    }

    // Check visibility permissions
    // If isPublic doesn't exist (legacy requirement), treat as public
    // If isPublic is false and user is not the creator, deny access
    const isPublic = requirement.isPublic !== undefined ? requirement.isPublic : true;
    const creatorId = requirement.creator._id.toString();

    // Allow access if requirement is public OR if user is the creator
    if (!isPublic && userId && creatorId !== userId) {
      return res.status(403).json({ message: "You don't have permission to view this requirement" });
    }

    res.status(200).json(requirement);
  } catch (error) {
    console.error('Error fetching requirement by ID:', error);
    if (error.name === 'CastError') {
      res.status(400).json({ message: 'Invalid requirement ID format' });
    } else {
      res.status(500).json({ message: 'Failed to fetch requirement' });
    }
  }
};

const deleteRequirement = async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    const { id } = req.params;
    const propertyToDelete = await RequirementModel.findById(id).populate("creator");
    if (!propertyToDelete) {
      throw new Error('Property not found');
    }
    await propertyToDelete.deleteOne({ session });

    const user = propertyToDelete.creator;
    user.allRequirements.pull(propertyToDelete);
    await user.save({ session });

    await session.commitTransaction();
    res.status(200).json({ message: "Property deleted successfully" });
  } catch (error) {
    await session.abortTransaction();
    console.error('Error deleting requirement:', error);
    if (error.message === 'Property not found') {
      res.status(404).json({ message: "Property not found" });
    } else {
      res.status(500).json({ message: "Failed to delete property" });
    }
  } finally {
    session.endSession();
  }
};

const updateRequirement = async (req, res) => {
  try {
    const { id } = req.params;
    const { title, description, propertyType, dealType, askedPrice, phone, location, isPublic = true } = req.body;

    const updatedRequirement = await RequirementModel.findByIdAndUpdate(
      { _id: id },
      {
        title,
        description,
        propertyType,
        dealType,
        phone,
        location,
        askedPrice,
        isPublic: Boolean(isPublic), // Add visibility field
      },
      { new: true, runValidators: true }
    );

    if (!updatedRequirement) {
      return res.status(404).json({ message: "Requirement not found" });
    }

    res.status(200).json({ message: "Requirement updated successfully", requirement: updatedRequirement });
  } catch (error) {
    console.error('Error updating requirement:', error);
    if (error.name === 'ValidationError') {
      res.status(400).json({ message: "Invalid input data", errors: error.errors });
    } else if (error.name === 'CastError') {
      res.status(400).json({ message: "Invalid requirement ID format" });
    } else {
      res.status(500).json({ message: "Failed to update requirement" });
    }
  }
};

const getTopLatestRequirements = async (req, res) => {
  try {
    // Only get public requirements for the dashboard (including legacy requirements)
    const latestRequirements = await RequirementModel.aggregate([
      {
        $match: {
          $or: [
            { isPublic: true },
            { isPublic: { $exists: false } } // Include legacy requirements
          ]
        }
      },
      { $sample: { size: 3 } }
    ]).exec();

    const publicQuery = { $or: [{ isPublic: true }, { isPublic: { $exists: false } }] };
    const totalRequirementsCount = await RequirementModel.countDocuments(publicQuery);

    res.status(200).json({
      requirements: latestRequirements,
      totalRequirementsCount
    });
  } catch (error) {
    console.error('Error fetching latest requirements:', error);
    res.status(500).json({ message: 'Failed to fetch latest requirements' });
  }
};

export { 
  updateRequirement, 
  getTopLatestRequirements, 
  getRequirementById, 
  deleteRequirement,
  getAllRequirements,
  saveRequirement,
};