import User from "../mongodb/models/user.js";


const getAllUsers = async (req, res) => {
  try {
    const users = await User.find({}).lean();


    res.status(200).json(users);
  } catch (error) {
    console.error('Error in getAllUsers:', error);
    res.status(500).json({ message: 'An error occurred while fetching users' });
  }
};

const createUser = async (req, res) => {
  try {
    const { name, email, avatar } = req.body;

    if (!name || !email) {
      return res.status(400).json({ message: 'Name and email are required' });
    }

    const userExists = await User.findOne({ email }).lean();

    if (userExists) return res.status(200).json(userExists);

    const newUser = await User.create({
      name,
      email,
      avatar,
    });

    res.status(201).json(newUser);
  } catch (error) {
    console.error('Error in createUser:', error);
    res.status(500).json({ message: 'An error occurred while creating the user' });
  }
};


const updateUser = async (req, res) => {
  try {
    console.log('Request body received:', req.body);

    const { name, email, phoneNumber, workLocation, reraNumber, services } = req.body;


    if (!email) {
      console.log('Email is required for updating user details');
      return res.status(400).json({ message: 'Email is required' });
    }


    const userExists = await User.findOne({ email }).lean();

    // Log services information if provided
    if (services) {
      console.log('Services information received:');
      console.log(services);
    }

    if (userExists) {
      const updateData = {
        name,
        phoneNumber,
        workLocation,
        reraNumber,
      };

      // Add services to update data if provided
      if (services) {
        updateData.services = {
          buySell: services.buySell || false,
          rent: services.rent || false,
          commercial: services.commercial || false,
          residential: services.residential || false
        };
      }

      const updatedUser = await User.findByIdAndUpdate(
        userExists._id,
        updateData,
        { new: true }
      );


      console.log(`User updated successfully`);
      return res.status(200).json(updatedUser);
    } else {
      console.log(`User with email: ${email} does not exist`);
      return res.status(404).json({ message: 'User not found' });
    }
  } catch (error) {
    console.error('Error in updateUser:', error);
    res.status(500).json({ message: 'An error occurred while updating the user' });
  }
};


const getUserInfoByID = async (req, res) => {
  try {
    const { id } = req.params;


    if (!id) {
      return res.status(400).json({ message: 'User ID is required' });
    }

    const user = await User.findOne({ _id: id }).populate("allProperties").populate("allRequirements").lean();


    if (user) {
      res.status(200).json(user);
    } else {
      res.status(404).json({ message: "User not found" });
    }
  } catch (error) {
    console.error('Error in getUserInfoByID:', error);
    if (error.name === 'CastError') {
      return res.status(400).json({ message: 'Invalid user ID format' });
    }
    res.status(500).json({ message: 'An error occurred while fetching user information' });
  }
};


// Function to set superuser status for specific emails
const setSuperUserStatus = async () => {
  try {
    // List of email addresses that should have superuser status
    const superUserEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];

    // Update all users with these emails to have superuser status
    const result = await User.updateMany(
      { email: { $in: superUserEmails } },
      { $set: { isSuperUser: true } }
    );

    // Reset superuser status for users not in the list
    await User.updateMany(
      { email: { $nin: superUserEmails } },
      { $set: { isSuperUser: false } }
    );

    console.log(`SuperUser status updated: ${result.modifiedCount} users modified`);
    return result;
  } catch (error) {
    console.error('Error setting superuser status:', error);
    throw error;
  }
};

// Modify createUser to check and set superuser status
const createUserWithSuperUserCheck = async (req, res) => {
  try {
    const { name, email, avatar } = req.body;

    if (!name || !email) {
      return res.status(400).json({ message: 'Name and email are required' });
    }

    const userExists = await User.findOne({ email }).lean();

    if (userExists) {
      // Check if this user should be a superuser
      const superUserEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
      const shouldBeSuperUser = superUserEmails.includes(email);

      // Update superuser status if needed
      if (userExists.isSuperUser !== shouldBeSuperUser) {
        await User.findByIdAndUpdate(
          userExists._id,
          { isSuperUser: shouldBeSuperUser },
          { new: true }
        );
      }

      // Get the updated user
      const updatedUser = await User.findById(userExists._id).lean();
      return res.status(200).json(updatedUser);
    }

    // Check if new user should be a superuser
    const superUserEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
    const isSuperUser = superUserEmails.includes(email);

    const newUser = await User.create({
      name,
      email,
      avatar,
      isSuperUser
    });

    res.status(201).json(newUser);
  } catch (error) {
    console.error('Error in createUser:', error);
    res.status(500).json({ message: 'An error occurred while creating the user' });
  }
};

export { getAllUsers, createUserWithSuperUserCheck as createUser, getUserInfoByID, updateUser, setSuperUserStatus };

