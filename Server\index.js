import express from "express";
import * as dotenv from "dotenv";
import cors from "cors";
import { rateLimit } from "express-rate-limit";

import connectDB from "./mongodb/connect.js";
import userRouter from "./routes/user.routes.js";
import propertyRouter from "./routes/property.routes.js";
import requirementRouter from "./routes/requirement.routes.js";
import exclusivePropertyRouter from "./routes/exclusiveProperty.routes.js";
import helmet from "helmet";
import { setSuperUserStatus } from "./controllers/user.controller.js";

dotenv.config();


const app = express();

app.use(cors({
  origin: ['http://localhost:5173', 'https://www.brickbix.in', 'https://brickbix.in'],
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Origin', 'X-Requested-With', 'Accept'],
  credentials: true
}));

app.use(express.json({ limit: "50mb" }));
app.use(helmet.contentSecurityPolicy({
  directives: {
    defaultSrc: ["'self'"],
    frameAncestors: ["'self'", "https://accounts.google.com"],
    scriptSrc: ["'self'", "https://apis.google.com"],
    // Add other directives as needed
  },
}));

// Configure API rate limiting
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  limit: 100, // Limit each IP to 100 requests per windowMs
  standardHeaders: 'draft-7', // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  message: {
    status: 429,
    message: 'Too many requests, please try again later.',
  },
  // Skip rate limiting for local development
  skip: (req) => {
    const clientIp = req.ip || req.connection.remoteAddress;
    return clientIp === '127.0.0.1' || clientIp === '::1';
  },
});

// Apply rate limiting to all API routes
app.use('/api/', apiLimiter);

// Configure stricter rate limiting for authentication and sensitive operations
const authLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  limit: 10, // Limit each IP to 10 requests per windowMs
  standardHeaders: 'draft-7',
  legacyHeaders: false,
  message: {
    status: 429,
    message: 'Too many authentication attempts, please try again later.',
  },
  // Skip rate limiting for local development
  skip: (req) => {
    const clientIp = req.ip || req.connection.remoteAddress;
    return clientIp === '127.0.0.1' || clientIp === '::1';
  },
});

// Apply stricter rate limiting to exclusive property operations
const exclusivePropertyLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  limit: 20, // Limit each IP to 20 requests per windowMs
  standardHeaders: 'draft-7',
  legacyHeaders: false,
  message: {
    status: 429,
    message: 'Too many requests to exclusive properties, please try again later.',
  },
  // Skip rate limiting for local development
  skip: (req) => {
    const clientIp = req.ip || req.connection.remoteAddress;
    return clientIp === '127.0.0.1' || clientIp === '::1';
  },
});

app.get("/", (req, res) => {
  res.send({ message: "Hello From BrickBix!" });
});

// Apply rate limiters to specific routes
app.use("/api/v1/users", authLimiter, userRouter);
app.use("/api/v1/properties", propertyRouter);
app.use("/api/v1/requirement", requirementRouter);
app.use("/api/v1/exclusive-properties", exclusivePropertyLimiter, exclusivePropertyRouter);

const startServer = async () => {
  try {
    // Connect to MongoDB
    await connectDB(process.env.MONGODB_URL);

    // Set superuser status for specific emails
    console.log("Setting up superuser permissions...");
    await setSuperUserStatus();
    console.log("Superuser permissions configured successfully");

    // Start the server
    app.listen(8080, () =>
      console.log("Server started on port http://localhost:8080"),
    );
  } catch (error) {
    console.log("Server startup error:", error);
  }
};

export default app;

startServer();
