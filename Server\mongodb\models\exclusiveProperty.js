import mongoose from "mongoose";

const ExclusivePropertySchema = new mongoose.Schema({
  title: { type: String, required: true, index: true },
  description: { type: String, required: true },
  propertyType: { type: String, required: true, index: true },
  location: { type: String, required: true, index: true },
  priceRange: {
    min: { type: Number, required: true },
    max: { type: Number, required: true }
  },
  typology: { type: String, required: true }, // 1BHK, 2BHK, etc.
  commissionRange: {
    min: { type: Number, required: true },
    max: { type: Number, required: true }
  },
  possessionDate: { type: Date, required: true },
  configuration: { type: String, required: true }, // Size/configuration details
  reraNumber: { type: String, required: true },
  totalArea: { type: Number, required: true }, // Total area of the society in sq ft
  paymentPlan: [{
    installment: { type: String, required: true },
    percentage: { type: Number, required: true },
    description: { type: String }
  }],
  totalBookings: { type: Number, required: true },
  websiteLink: { type: String, default: '' },
  brochureLink: { type: String, default: '' },
  gpsLocation: { type: String, default: '' }, // Google Maps location link
  floorPlanLinks: [{ type: String }],
  images: [{ type: String, required: true }], // Array of image URLs
  creator: { type: mongoose.Schema.Types.ObjectId, ref: "User", required: true },
  isSuperUser: { type: Boolean, default: false }, // Flag to indicate if creator is a super user
  active: { type: Boolean, default: true },
  viewCount: { type: Number, default: 0 }, // Number of times the property has been viewed
  shareCount: { type: Number, default: 0 } // Number of times the property has been shared
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    currentTime: () => new Date(Date.now() + 19800000) // Adjusting to IST
  },
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Add virtual field for formatted price
ExclusivePropertySchema.virtual('formattedPrice').get(function() {
  return `₹${this.priceRange.min.toLocaleString('en-IN')} - ₹${this.priceRange.max.toLocaleString('en-IN')}`;
});

// Add virtual field for formatted commission
ExclusivePropertySchema.virtual('formattedCommission').get(function() {
  return `${this.commissionRange.min}% - ${this.commissionRange.max}%`;
});

// Add virtual field for formatted possession date
ExclusivePropertySchema.virtual('formattedPossessionDate').get(function() {
  return this.possessionDate ? this.possessionDate.toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'long'
  }) : '';
});

// Add indexes for better query performance
ExclusivePropertySchema.index({ active: 1 });
ExclusivePropertySchema.index({ creator: 1 });
ExclusivePropertySchema.index({ createdAt: -1 });

const exclusivePropertyModel = mongoose.model("ExclusiveProperty", ExclusivePropertySchema);

export default exclusivePropertyModel;