import mongoose from "mongoose";

const PropertySchema = new mongoose.Schema({
  title: { type: String, required: true, index: true },
  description: { type: String, required: true },
  propertyType: { type: String, required: true, index: true },
  dealType: { type: String, required: true },
  location: { type: String, required: true, index: true },
  price: { type: Number, required: true },
  totalSquareFeet: { type: Number, required: true },
  phone: { type: Number, required: true },
  photos: [{ type: String }], // Changed from photo: String to photos: [String] - Array of strings to store photo URLs
  creator: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
  isPublic: { type: Boolean, default: true, index: true }, // Visibility selector: true = public, false = private
},{
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    currentTime: () => new Date(Date.now() + 19800000) // Adjusting to IST
  }
});

const propertyModel = mongoose.model("Property", PropertySchema);

export default propertyModel;