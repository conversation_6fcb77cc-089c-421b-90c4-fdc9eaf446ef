import mongoose from "mongoose";

const RequirementSchema = new mongoose.Schema({
  title: { type: String, required: true, index: true },
  description: { type: String, required: true },
  propertyType: { type: String, required: true, index: true },
  dealType: { type: String, required: true },
  phone: { type: Number, required: true },
  askedPrice: { type: Number, required: true }, // Changed from "price" to "askedPrice"
  location: { type: String, required: true, index: true },
  creator: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
  isPublic: { type: Boolean, default: true, index: true }, // Visibility selector: true = public, false = private
},{
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    currentTime: () => new Date(Date.now() + 19800000) // Adjusting to IST
  }
});

const requirementModel = mongoose.model("Requirement", RequirementSchema);

export default requirementModel;