import mongoose from "mongoose";

const UserSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  phoneNumber: { type: String, required:false },
  workLocation: { type:String, required:false },
  reraNumber: { type:String, required:false },
  avatar: { type: String, required: false },
  services: {
    buySell: { type: Boolean, default: false },
    rent: { type: Boolean, default: false },
    commercial: { type: Boolean, default: false },
    residential: { type: Boolean, default: false },
  },
  allProperties: [{ type: mongoose.Schema.Types.ObjectId, ref: "Property" }],
  allRequirements: [{ type: mongoose.Schema.Types.ObjectId, ref: "Requirement" }],
  allExclusiveProperties: [{ type: mongoose.Schema.Types.ObjectId, ref: "ExclusiveProperty" }],
  isSuperUser: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    currentTime: () => new Date(Date.now() + 19800000)
  }
});

const userModel = mongoose.model("User", UserSchema);

export default userModel;

