{"name": "server", "version": "1.0.0", "private": true, "description": "", "keywords": [], "license": "ISC", "author": "", "type": "module", "main": "index.js", "scripts": {"start": "nodemon index", "lint": "eslint . --ext .js", "lint:fix": "eslint . --ext .js --fix", "format": "prettier --write \"**/*.js\""}, "dependencies": {"axios": "^1.8.4", "cloudinary": "^1.33.0", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-fileupload": "^1.5.1", "express-rate-limit": "^7.5.0", "helmet": "^8.0.0", "mongoose": "^6.13.2", "puppeteer": "^24.4.0"}, "devDependencies": {"eslint": "^8.38.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsdoc": "^46.4.3", "eslint-plugin-prettier": "^4.2.1", "nodemon": "^3.1.7", "prettier": "^2.8.8"}}