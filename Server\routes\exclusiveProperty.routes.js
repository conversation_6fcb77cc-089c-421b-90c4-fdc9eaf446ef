import express from 'express';
import { rateLimit } from 'express-rate-limit';
import {
  getAllExclusiveProperties,
  getExclusivePropertyDetail,
  getExclusivePropertyBySlug,
  createExclusiveProperty,
  updateExclusiveProperty,
  deleteExclusiveProperty,
  checkSuperUser,
  trackPropertyShare,
  trackPropertyView,
} from '../controllers/exclusiveProperty.controller.js';

const router = express.Router();

// Rate limiter for write operations (create, update, delete)
const writeOperationsLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  limit: 10, // Limit each IP to 10 write operations per 15 minutes
  standardHeaders: 'draft-7',
  legacyHeaders: false,
  message: {
    status: 429,
    message: 'Too many write operations attempted, please try again later.',
  },
});

// Rate limiter for analytics tracking
const analyticsLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  limit: 50, // Limit each IP to 50 tracking operations per 5 minutes
  standardHeaders: 'draft-7',
  legacyHeaders: false,
  message: {
    status: 429,
    message: 'Too many tracking operations attempted, please try again later.',
  },
});

// Main routes
router.get('/', getAllExclusiveProperties);
router.post('/', writeOperationsLimiter, createExclusiveProperty);

// User verification
router.get('/check-superuser/:email', checkSuperUser);

// Property details by ID or slug
router.get('/:id', getExclusivePropertyDetail);
router.get('/by-slug/:slug', getExclusivePropertyBySlug);

// Property updates with rate limiting
router.patch('/:id', writeOperationsLimiter, updateExclusiveProperty);
router.delete('/:id', writeOperationsLimiter, deleteExclusiveProperty);

// Analytics tracking with rate limiting
router.post('/:id/track-share', analyticsLimiter, trackPropertyShare);
router.post('/:id/track-view', analyticsLimiter, trackPropertyView);

export default router;