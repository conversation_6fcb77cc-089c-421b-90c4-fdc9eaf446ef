import express from "express";
import { rateLimit } from "express-rate-limit";

import {
  createProperty,
  deleteProperty,
  getAllProperties,
  getPropertyDetail,
  updateProperty,
  getTopLatestProperties,
} from "../controllers/property.controller.js";

const router = express.Router();

// Rate limiter for property write operations
const propertyWriteLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  limit: 15, // Limit each IP to 15 write operations per 15 minutes
  standardHeaders: 'draft-7',
  legacyHeaders: false,
  message: {
    status: 429,
    message: 'Too many property write operations attempted, please try again later.',
  },
});

router.route("/").get(getAllProperties);
router.route("/five").get(getTopLatestProperties);
router.route("/:id").get(getPropertyDetail);
router.route("/").post(propertyWriteLimiter, createProperty);
router.route("/:id").patch(propertyWriteLimiter, updateProperty);
router.route("/:id").delete(propertyWriteLimiter, deleteProperty);

export default router;
