import express from "express";
import { rateLimit } from "express-rate-limit";

import {
    saveRequirement,
    getAllRequirements,
    getRequirementById,
    deleteRequirement,
    updateRequirement,
    getTopLatestRequirements,
} from "../controllers/requirement.controller.js";

const router = express.Router();

// Rate limiter for requirement operations
const requirementLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  limit: 20, // Limit each IP to 20 operations per 15 minutes
  standardHeaders: 'draft-7',
  legacyHeaders: false,
  message: {
    status: 429,
    message: 'Too many requirement operations attempted, please try again later.',
  },
});

router.route("/").post(requirementLimiter, saveRequirement);
router.route("/").get(getAllRequirements);
router.route("/five").get(getTopLatestRequirements);
router.route("/:id").get(getRequirementById);
router.route("/:id").delete(requirementLimiter, deleteRequirement);
router.route("/:id").patch(requirementLimiter, updateRequirement);

export default router;
