import express from "express";
import { rateLimit } from "express-rate-limit";

import {
  createUser,
  getAllUsers,
  getUserInfoByID,
  updateUser,
  setSuperUserStatus,
} from "../controllers/user.controller.js";

const router = express.Router();

// Super strict rate limiting for sensitive operations
const superUserLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  limit: 5, // Limit each IP to 5 requests per hour
  standardHeaders: 'draft-7',
  legacyHeaders: false,
  message: {
    status: 429,
    message: 'Too many sensitive operations attempted, please try again later.',
  },
});

router.route("/").get(getAllUsers);
router.route("/").post(createUser);
router.route("/update").post(updateUser);
router.route("/:id").get(getUserInfoByID);

// Route to set superuser status - this should be protected in production
router.route("/set-superusers").get(superUserLimiter, async (req, res) => {
  try {
    const result = await setSuperUserStatus();
    res.status(200).json({
      message: "Superuser status updated successfully",
      modifiedCount: result.modifiedCount
    });
  } catch (error) {
    console.error("Error setting superuser status:", error);
    res.status(500).json({ message: "Failed to update superuser status" });
  }
});

export default router;
