// Migration script to set default visibility for existing properties and requirements
import mongoose from 'mongoose';
import Property from '../mongodb/models/property.js';
import RequirementModel from '../mongodb/models/requirment.js';
import dotenv from 'dotenv';

dotenv.config();

const migrateVisibility = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URL);
    console.log('Connected to MongoDB');

    // Update properties without isPublic field to be public by default
    const propertyResult = await Property.updateMany(
      { isPublic: { $exists: false } },
      { $set: { isPublic: true } }
    );
    console.log(`Updated ${propertyResult.modifiedCount} properties to be public by default`);

    // Update requirements without isPublic field to be public by default
    const requirementResult = await RequirementModel.updateMany(
      { isPublic: { $exists: false } },
      { $set: { isPublic: true } }
    );
    console.log(`Updated ${requirementResult.modifiedCount} requirements to be public by default`);

    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
};

// Run the migration
migrateVisibility();
