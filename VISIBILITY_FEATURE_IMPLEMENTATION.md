# Visibility Selector Feature Implementation

## Overview
This document outlines the implementation of a visibility selector feature for properties and requirements in the BrickBix application. Users can now control whether their listings are public (visible to all users) or private (visible only to the creator).

## Changes Made

### 1. Database Schema Updates

#### Property Model (`Server/mongodb/models/property.js`)
- Added `isPublic` field with type Boolean, default true, and indexed for performance
- This field controls whether a property is visible to all users or only to its creator

#### Requirement Model (`Server/mongodb/models/requirment.js`)
- Added `isPublic` field with type <PERSON>olean, default true, and indexed for performance
- This field controls whether a requirement is visible to all users or only to its creator

### 2. Backend API Updates

#### Property Controller (`Server/controllers/property.controller.js`)
- **getAllProperties**: Modified to filter properties based on visibility and user ownership
  - If `userId` is provided: shows public properties + user's own private properties
  - If no `userId`: shows only public properties
- **getPropertyDetail**: Added permission check for private properties
- **createProperty**: Added `isPublic` field to property creation
- **updateProperty**: Added `isPublic` field to property updates
- **getTopLatestProperties**: Only returns public properties for dashboard

#### Requirement Controller (`Server/controllers/requirement.controller.js`)
- **getAllRequirements**: Modified to filter requirements based on visibility and user ownership
  - If `userId` is provided: shows public requirements + user's own private requirements
  - If no `userId`: shows only public requirements
- **getRequirementById**: Added permission check for private requirements
- **saveRequirement**: Added `isPublic` field to requirement creation
- **updateRequirement**: Added `isPublic` field to requirement updates
- **getTopLatestRequirements**: Only returns public requirements for dashboard

### 3. Frontend Form Updates

#### Property Form (`Client/src/components/common/Form.tsx`)
- Added visibility selector with Material-UI Switch component
- Added visual indicators (Visibility/VisibilityOff icons)
- Added descriptive text explaining public vs private visibility
- Added `initialVisibility` prop for edit mode
- Updated form submission to include `isPublic` field

#### Requirement Form (`Client/src/components/common/RequirementForm.tsx`)
- Added visibility selector with Material-UI Switch component
- Added visual indicators (Visibility/VisibilityOff icons)
- Added descriptive text explaining public vs private visibility
- Added `initialVisibility` prop for edit mode
- Updated form submission to include `isPublic` field

### 4. Frontend Page Updates

#### All Properties Page (`Client/src/pages/all-properties.tsx`)
- Added `useGetIdentity` to get current user ID
- Added permanent filter to pass `userId` to API for proper visibility filtering

#### All Requirements Page (`Client/src/pages/all-requirement.tsx`)
- Added `useGetIdentity` to get current user ID
- Added permanent filter to pass `userId` to API for proper visibility filtering

#### Property Details Page (`Client/src/pages/property-details.tsx`)
- Updated to pass `userId` in meta for permission checking
- Added proper TypeScript typing for user identity

#### Edit Property Page (`Client/src/pages/edit-property.tsx`)
- Added `initialVisibility` prop to pass current property visibility state to form

#### Edit Requirement Page (`Client/src/pages/edit-requirement.tsx`)
- Added `useOne` hook to fetch current requirement data
- Added `initialVisibility` prop to pass current requirement visibility state to form

### 5. Data Provider Updates

#### REST Data Provider (`Client/src/rest-data-provider/index.ts`)
- **getOne**: Modified to pass `userId` as query parameter for permission checking
- **getList**: Modified to handle `userId` from meta for visibility filtering

### 6. Interface Updates

#### Common Interfaces (`Client/src/interfaces/common.d.ts`)
- Added `initialVisibility?: boolean` to `RequirementFormProps` interface

## Feature Behavior

### Public Listings (isPublic: true)
- Visible to all users in the application
- Appear in general property/requirement browsing
- Included in dashboard statistics and random selections
- Can be viewed by anyone with the direct link

### Private Listings (isPublic: false)
- Only visible to the creator/owner
- Do not appear in general browsing for other users
- Not included in public dashboard statistics
- Return 403 Forbidden error if accessed by non-owners

### User Experience
- **Create Mode**: Visibility selector defaults to "Public"
- **Edit Mode**: Visibility selector shows current state of the listing
- **Visual Feedback**: Clear icons and text indicate current visibility setting
- **Responsive Design**: Visibility selector works on all device sizes

## API Endpoints Affected

### Properties
- `GET /api/v1/properties` - Now filters by visibility and user ownership
- `GET /api/v1/properties/:id` - Now checks permissions for private properties
- `POST /api/v1/properties` - Now accepts `isPublic` field
- `PATCH /api/v1/properties/:id` - Now accepts `isPublic` field

### Requirements
- `GET /api/v1/requirement` - Now filters by visibility and user ownership
- `GET /api/v1/requirement/:id` - Now checks permissions for private requirements
- `POST /api/v1/requirement` - Now accepts `isPublic` field
- `PATCH /api/v1/requirement/:id` - Now accepts `isPublic` field

## Security Considerations

1. **Server-side Validation**: All visibility checks are performed on the backend
2. **Permission Enforcement**: Private listings return 403 errors for unauthorized access
3. **Data Filtering**: Database queries filter results based on user permissions
4. **No Client-side Security**: Frontend only provides UI; all security is backend-enforced

## Testing Instructions

### 1. Create New Listings
1. Navigate to create property/requirement forms
2. Verify visibility selector is present and defaults to "Public"
3. Toggle between public and private settings
4. Create listings with both visibility settings

### 2. Edit Existing Listings
1. Edit a property/requirement
2. Verify visibility selector shows current state
3. Change visibility setting and save
4. Verify changes are persisted

### 3. Visibility Behavior Testing
1. Create a private listing with User A
2. Log in as User B
3. Verify private listing is not visible in general browsing
4. Verify direct access to private listing returns permission error
5. Log back in as User A
6. Verify User A can see their own private listings

### 4. Public Listings
1. Create a public listing with any user
2. Verify it appears in general browsing for all users
3. Verify it can be accessed directly by any user

## Database Migration

For existing data, all properties and requirements will default to `isPublic: true` due to the schema default value. No manual migration is required.

## Performance Considerations

- Added database indexes on `isPublic` field for efficient filtering
- Optimized queries to use compound conditions for visibility and ownership
- Minimal impact on existing API performance

## Future Enhancements

1. **Bulk Visibility Management**: Allow users to change visibility of multiple listings at once
2. **Visibility Analytics**: Track how visibility settings affect listing performance
3. **Shared Private Listings**: Allow private listings to be shared with specific users
4. **Visibility Scheduling**: Allow users to schedule when listings become public/private
