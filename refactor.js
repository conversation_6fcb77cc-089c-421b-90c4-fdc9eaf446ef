/**
 * This script helps with the refactoring process by running linting and formatting tools
 * on the codebase.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  underscore: '\x1b[4m',
  blink: '\x1b[5m',
  reverse: '\x1b[7m',
  hidden: '\x1b[8m',
  
  fg: {
    black: '\x1b[30m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    crimson: '\x1b[38m'
  },
  
  bg: {
    black: '\x1b[40m',
    red: '\x1b[41m',
    green: '\x1b[42m',
    yellow: '\x1b[43m',
    blue: '\x1b[44m',
    magenta: '\x1b[45m',
    cyan: '\x1b[46m',
    white: '\x1b[47m',
    crimson: '\x1b[48m'
  }
};

/**
 * Executes a command and returns its output
 * @param {string} command - The command to execute
 * @param {string} cwd - The working directory
 * @returns {string} The command output
 */
function runCommand(command, cwd) {
  try {
    return execSync(command, { cwd, stdio: 'pipe' }).toString();
  } catch (error) {
    console.error(`${colors.fg.red}Error executing command: ${command}${colors.reset}`);
    console.error(error.stdout.toString());
    return error.stdout.toString();
  }
}

/**
 * Prints a section header to the console
 * @param {string} title - The section title
 */
function printHeader(title) {
  console.log('\n' + colors.bg.blue + colors.fg.white + colors.bright + ' ' + title + ' ' + colors.reset + '\n');
}

/**
 * Installs dependencies for a project
 * @param {string} projectPath - The path to the project
 */
function installDependencies(projectPath) {
  printHeader(`Installing dependencies in ${projectPath}`);
  runCommand('npm install', projectPath);
}

/**
 * Runs ESLint on a project
 * @param {string} projectPath - The path to the project
 * @param {boolean} fix - Whether to fix issues automatically
 */
function runEslint(projectPath, fix = false) {
  const command = fix ? 'npm run lint:fix' : 'npm run lint';
  printHeader(`Running ESLint in ${projectPath}${fix ? ' (fixing issues)' : ''}`);
  runCommand(command, projectPath);
}

/**
 * Formats code with Prettier
 * @param {string} projectPath - The path to the project
 */
function formatCode(projectPath) {
  printHeader(`Formatting code in ${projectPath}`);
  runCommand('npm run format', projectPath);
}

/**
 * Runs TypeScript type checking
 * @param {string} projectPath - The path to the project
 */
function typeCheck(projectPath) {
  printHeader(`Running TypeScript type checking in ${projectPath}`);
  runCommand('npm run type-check', projectPath);
}

// Main execution
(async function main() {
  const rootDir = process.cwd();
  const clientDir = path.join(rootDir, 'Client');
  const serverDir = path.join(rootDir, 'Server');
  
  // Check if directories exist
  if (!fs.existsSync(clientDir)) {
    console.error(`${colors.fg.red}Client directory not found: ${clientDir}${colors.reset}`);
    process.exit(1);
  }
  
  if (!fs.existsSync(serverDir)) {
    console.error(`${colors.fg.red}Server directory not found: ${serverDir}${colors.reset}`);
    process.exit(1);
  }
  
  // Install dependencies
  installDependencies(clientDir);
  installDependencies(serverDir);
  
  // Run linting and formatting
  runEslint(clientDir, true);
  formatCode(clientDir);
  typeCheck(clientDir);
  
  runEslint(serverDir, true);
  formatCode(serverDir);
  
  printHeader('Refactoring complete!');
  console.log(`${colors.fg.green}The codebase has been refactored according to the new linting rules.${colors.reset}`);
  console.log(`${colors.fg.yellow}Please review the changes and make any necessary adjustments.${colors.reset}`);
  console.log(`${colors.fg.cyan}See LINTING_RULES.md for more information about the linting rules.${colors.reset}`);
})();
